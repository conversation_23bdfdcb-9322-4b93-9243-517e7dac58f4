import React, { useState } from 'react';
import { WorkflowNodeType } from '~/background/workflow/types';
import { WebContext, WebInteractionResult } from '~/types';

type Props = { onBack: () => void };

function isWorkflowNodeType(node: any): node is WorkflowNodeType {
  return node && typeof node === 'object' && 'type' in node;
}

function sendExecuteTool(name: string, args: Record<string, unknown> = {}) {
  return new Promise<WebInteractionResult<unknown>>((resolve, reject) => {
    try {
      chrome.runtime.sendMessage(
        {
          name: 'execute-tool',
          body: { name, arguments: args },
        },
        (res: WebInteractionResult<any>) => {
          // Check for Chrome runtime errors
          if (chrome.runtime.lastError) {
            reject(new Error(`Chrome runtime error: ${chrome.runtime.lastError.message}`));
            return;
          }

          // Check if the response indicates an error
          if (res && !res.success && res.error) {
            resolve(res); // Let the calling code handle tool-level errors
            return;
          }

          resolve(res);
        }
      );
    } catch (error) {
      reject(
        new Error(
          `Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`
        )
      );
    }
  });
}

function sendWorkflowNodeCommand(nodeOrCode: WorkflowNodeType | string) {
  return new Promise<WebInteractionResult<any>>(resolve => {
    let body;
    if (typeof nodeOrCode === 'string') {
      // Legacy support for string input
      body = { node: { type: 'script', code: nodeOrCode, id: 'script-node' } };
    } else if (isWorkflowNodeType(nodeOrCode)) {
      // New support for node object
      body = { node: nodeOrCode };
    } else {
      throw new Error('Invalid input type for sendWorkflowNodeCommand');
    }

    chrome.runtime.sendMessage(
      {
        name: 'workflow:executeNode',
        body,
      },
      (res: WebInteractionResult<any>) => {
        resolve(res);
      }
    );
  });
}

function sendWorkflowCommand(
  workflow: WorkflowNodeType[],
  parameters: Record<string, unknown> = {}
) {
  return new Promise<WebInteractionResult<any>>(resolve => {
    chrome.runtime.sendMessage(
      {
        name: 'workflow:execute',
        body: { workflow, parameters },
      },
      (res: WebInteractionResult<any>) => {
        resolve(res);
      }
    );
  });
}

const TestWebToolkit: React.FC<Props> = ({ onBack }) => {
  const [loading, setLoading] = useState<string | null>(null);
  const [result, setResult] = useState<WebInteractionResult<any> | null>(null);
  const [selectorOrIndex, setSelectorOrIndex] = useState<string>('0');
  const [copied, setCopied] = useState<boolean>(false);
  const [inputValue, setInputValue] = useState<string>('hello');
  const [inputClearFirst, setInputClearFirst] = useState<boolean>(true);
  const [inputPressEnter, setInputPressEnter] = useState<boolean>(false);
  const [keys, setKeys] = useState<string>('Escape');
  const [refreshTimeout, setRefreshTimeout] = useState<number>(5000);
  const [analyzeSelector, setAnalyzeSelector] = useState<string>('');
  const [webContext, setWebContext] = useState<WebContext | null>(null);
  const [generateDiff, setGenerateDiff] = useState<boolean>(false);

  const run = async (tool: string, args: Record<string, unknown> = {}) => {
    try {
      setLoading(tool);
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Tool execution timeout (30 seconds)')), 10000);
      });

      args.context = webContext;
      const res = await Promise.race([sendExecuteTool(`WebToolkit_${tool}`, args), timeoutPromise]);
      if (res?.context) {
        setWebContext(res.context);
      }
      setResult(res ?? { success: false, error: 'no response' });
    } catch (error) {
      console.error('---[error] TestWebToolkit tool execution failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setResult({
        success: false,
        error: `Tool "${tool}" failed: ${errorMessage}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runWorkflowNodeCommand = async () => {
    try {
      setLoading('workflow:executeNode');
      const res = await sendWorkflowNodeCommand(`
  async function() {
    let el = null;
    while (!el) {
      el = document.querySelector('[data-testid="tweetTextarea_0"]');
      if (!el) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    const rect = el.getBoundingClientRect();
    return {x: rect.left + rect.width / 2, y: rect.top + rect.height / 2};
  }();
`);
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit workflow command failed:', error);
      setResult({
        success: false,
        error: `Workflow command failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runWorkflowCommand = async () => {
    try {
      setLoading('workflow:execute');
      const res2 = await sendWorkflowCommand(
        [
          {
            type: 'navigation',
            action: 'open',
            target: { url: 'https://x.com/compose/post' },
            id: 'open-compose-tweet',
          },
          {
            type: 'script',
            target: { tabId: '{{open-compose-tweet}}' },
            code: `
            let el = null;
            while (!el) {
              el = document.querySelector('div[role="textbox"]');
              if (!el) {
                await new Promise(resolve => setTimeout(resolve, 100));
              }
            }
            const rect = el.getBoundingClientRect();
            return {x: rect.left + rect.width/2, y: rect.top + rect.height/2};
          `,
            id: 'get-text-area-coords',
          },
          {
            id: 'click-text-area',
            type: 'debugger',
            command: 'Input.dispatchMouseEvent',
            action: 'command',
            params: {
              y: '{{get-text-area-coords.y}}',
              x: '{{get-text-area-coords.x}}',
              type: 'mousePressed',
              button: 'left',
              clickCount: 1,
            },
            target: { tabId: '{{open-compose-tweet}}' },
          },
          {
            id: 'click-text-area-release',
            type: 'debugger',
            command: 'Input.dispatchMouseEvent',
            action: 'command',
            params: {
              y: '{{get-text-area-coords.y}}',
              x: '{{get-text-area-coords.x}}',
              type: 'mouseReleased',
              button: 'left',
              clickCount: 1,
            },
            target: { tabId: '{{open-compose-tweet}}' },
          },
          {
            id: 'type-tweet-content',
            type: 'debugger',
            command: 'Input.insertText',
            action: 'command',
            params: { text: '{{param.tweetContent}}' },
            target: { tabId: '{{open-compose-tweet}}' },
          },
          {
            id: 'get-post-button-coords',
            type: 'script',
            code: `
            const el = document.querySelector('button[data-testid="tweetButton"]');
            const rect = el.getBoundingClientRect();
            return {x: rect.left + rect.width/2, y: rect.top + rect.height/2};
          `,
            target: { tabId: '{{open-compose-tweet}}' },
          },
          {
            id: 'click-post-button',
            type: 'debugger',
            command: 'Input.dispatchMouseEvent',
            action: 'command',
            params: {
              y: '{{get-post-button-coords.y}}',
              x: '{{get-post-button-coords.x}}',
              type: 'mousePressed',
              button: 'left',
              clickCount: 1,
            },
            target: { tabId: '{{open-compose-tweet}}' },
          },
          {
            id: 'click-post-button-release',
            type: 'debugger',
            command: 'Input.dispatchMouseEvent',
            action: 'command',
            params: {
              y: '{{get-post-button-coords.y}}',
              x: '{{get-post-button-coords.x}}',
              type: 'mouseReleased',
              button: 'left',
              clickCount: 1,
            },
            target: { tabId: '{{open-compose-tweet}}' },
          },
        ],
        { tweetContent: 'Hello from Gemini!' }
      );
      setResult(res2);
    } catch (error) {
      console.error('---[error] TestWebToolkit workflow command failed:', error);
      setResult({
        success: false,
        error: `Workflow command failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runTwitterDMReplyWorkflow = async () => {
    try {
      setLoading('workflow:execute');
      const res = await sendWorkflowCommand(
        [
          {
            id: 'open-twitter',
            type: 'navigation',
            action: 'open',
            target: { url: 'https://twitter.com/messages' },
          },
          {
            id: 'wait-for-messages',
            type: 'wait',
            duration: 3000,
          },
          {
            id: 'click-first-conversation',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="conversation"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'wait-for-chat',
            type: 'wait',
            duration: 2000,
          },
          {
            id: 'click-message-input',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="dmComposerTextInput"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'type-reply',
            type: 'library',
            libraryId: 'lib:input',
            params: {
              selector: '[data-testid="dmComposerTextInput"]',
              value: '{{param.replyMessage}}',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'send-message',
            type: 'library',
            libraryId: 'lib:sendKeys',
            params: {
              keys: 'Enter',
              tabId: '{{open-twitter}}',
            },
          },
        ],
        {
          replyMessage: "Thanks for your message! I'll get back to you soon.",
        }
      );
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit Twitter DM reply workflow failed:', error);
      setResult({
        success: false,
        error: `Twitter DM reply workflow failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runTwitterDMReplyWithCustomMessage = async () => {
    try {
      setLoading('workflow:execute');
      const res = await sendWorkflowCommand(
        [
          {
            id: 'open-twitter',
            type: 'navigation',
            action: 'open',
            target: { url: 'https://twitter.com/messages' },
          },
          {
            id: 'wait-for-load',
            type: 'wait',
            duration: 3000,
          },
          {
            id: 'click-compose-dm',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="NewDM_Button"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'wait-for-compose',
            type: 'wait',
            duration: 2000,
          },
          {
            id: 'input-recipient',
            type: 'library',
            libraryId: 'lib:input',
            params: {
              selector: '[data-testid="searchPeople"]',
              value: '{{param.recipient}}',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'wait-for-search',
            type: 'wait',
            duration: 2000,
          },
          {
            id: 'select-first-result',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="TypeaheadUser"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'click-next',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="nextButton"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'wait-for-chat',
            type: 'wait',
            duration: 2000,
          },
          {
            id: 'type-message',
            type: 'library',
            libraryId: 'lib:input',
            params: {
              selector: '[data-testid="dmComposerTextInput"]',
              value: '{{param.message}}',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'send-dm',
            type: 'library',
            libraryId: 'lib:sendKeys',
            params: {
              keys: 'Enter',
              tabId: '{{open-twitter}}',
            },
          },
        ],
        {
          recipient: 'elonmusk',
          message: 'Hello! This is an automated message from my workflow system.',
        }
      );
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit Twitter DM with custom message failed:', error);
      setResult({
        success: false,
        error: `Twitter DM with custom message failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runLLMNodeCommand = async () => {
    try {
      setLoading('workflow:executeNode');
      const res = await sendWorkflowNodeCommand({
        id: 'test-llm',
        type: 'llm',
        prompt: 'Generate a creative tweet about AI and automation in 280 characters or less.',
        jsonSchema: `{
          "type": "object",
          "properties": {
            "tweet": { "type": "string", "maxLength": 280 }
          },
          "required": ["tweet"]
        }`,
      });
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit LLM node command failed:', error);
      setResult({
        success: false,
        error: `LLM node command failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runLLMWorkflowCommand = async () => {
    try {
      setLoading('workflow:execute');
      const res = await sendWorkflowCommand(
        [
          {
            id: 'open-twitter',
            type: 'navigation',
            action: 'open',
            target: { url: 'https://x.com/compose/post' },
          },
          {
            id: 'get-page-content',
            type: 'script',
            code: `
              return {
                title: document.title,
                url: window.location.href,
                text: document.body.innerText.substring(0, 1000)
              };
            `,
            target: { tabId: '{{open-twitter}}' },
          },
          {
            id: 'generate-tweet',
            type: 'llm',
            prompt:
              'Based on the page content, generate an engaging tweet about web automation and AI. Keep it under 280 characters.',
            jsonSchema: `{
              "type": "object",
              "properties": {
                "tweet": { "type": "string", "maxLength": 280 }
              },
              "required": ["tweet"]
            }`,
          },
          {
            id: 'get-textarea-coords',
            type: 'script',
            code: `
              let el = null;
              while (!el) {
                el = document.querySelector('div[role="textbox"]');
                if (!el) {
                  await new Promise(resolve => setTimeout(resolve, 100));
                }
              }
              const rect = el.getBoundingClientRect();
              return {x: rect.left + rect.width/2, y: rect.top + rect.height/2};
            `,
            target: { tabId: '{{open-twitter}}' },
          },
          {
            id: 'click-textarea',
            type: 'debugger',
            action: 'command',
            command: 'Input.dispatchMouseEvent',
            params: {
              type: 'mousePressed',
              x: '{{get-textarea-coords.x}}',
              y: '{{get-textarea-coords.y}}',
              button: 'left',
              clickCount: 1,
            },
            target: { tabId: '{{open-twitter}}' },
          },
          {
            id: 'type-generated-tweet',
            type: 'debugger',
            action: 'command',
            command: 'Input.insertText',
            params: { text: '{{generate-tweet}}' },
            target: { tabId: '{{open-twitter}}' },
          },
        ],
        {}
      );
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit LLM workflow command failed:', error);
      setResult({
        success: false,
        error: `LLM workflow command failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runInputLibraryCommand = async () => {
    try {
      setLoading('workflow:execute');
      const res = await sendWorkflowCommand(
        [
          {
            id: 'open-test-page',
            type: 'navigation',
            action: 'open',
            target: { url: 'https://example.com' },
          },
          {
            id: 'input-test',
            type: 'library',
            libraryId: 'lib:input',
            params: {
              selector: 'input[name="q"]',
              value: 'test input',
              tabId: '{{open-test-page}}',
            },
          },
        ],
        {}
      );
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit input library command failed:', error);
      setResult({
        success: false,
        error: `Input library command failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runSendKeysLibraryCommand = async () => {
    try {
      setLoading('workflow:execute');
      const res = await sendWorkflowCommand(
        [
          {
            id: 'open-test-page',
            type: 'navigation',
            action: 'open',
            target: { url: 'https://example.com' },
          },
          {
            id: 'press-enter',
            type: 'library',
            libraryId: 'lib:sendKeys',
            params: {
              keys: 'Enter',
              tabId: '{{open-test-page}}',
            },
          },
        ],
        {}
      );
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit sendKeys library command failed:', error);
      setResult({
        success: false,
        error: `SendKeys library command failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runIntelligentTwitterDMReply = async () => {
    try {
      setLoading('workflow:execute');
      const res = await sendWorkflowCommand(
        [
          {
            id: 'open-twitter',
            type: 'navigation',
            action: 'open',
            target: { url: 'https://twitter.com/messages' },
          },
          {
            id: 'wait-for-messages',
            type: 'wait',
            duration: 3000,
          },
          {
            id: 'click-first-conversation',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="conversation"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'wait-for-chat',
            type: 'wait',
            duration: 2000,
          },
          {
            id: 'extract-conversation',
            type: 'script',
            target: { tabId: '{{open-twitter}}' },
            code: `
              const messages = Array.from(document.querySelectorAll('[data-testid="tweetText"]'));
              const lastMessage = messages[messages.length - 1];
              return {
                lastMessage: lastMessage ? lastMessage.textContent : 'No message found',
                messageCount: messages.length,
                conversation: messages.slice(-3).map(m => m.textContent).join(' | ')
              };
            `,
          },
          {
            id: 'generate-reply',
            type: 'script',
            target: { tabId: '{{open-twitter}}' },
            code: `
              // For now, return a simple reply since LLMNode is not implemented yet
              const conversation = '{{extract-conversation.conversation}}';
              const lastMessage = '{{extract-conversation.lastMessage}}';
              
              // Simple reply logic
              if (lastMessage.includes('hello') || lastMessage.includes('hi')) {
                return 'Hello! How are you doing today?';
              } else if (lastMessage.includes('thanks') || lastMessage.includes('thank you')) {
                return "You're welcome! Happy to help.";
              } else {
                return "Thanks for your message! I'll get back to you soon.";
              }
            `,
          },
          {
            id: 'click-message-input',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="dmComposerTextInput"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'type-intelligent-reply',
            type: 'library',
            libraryId: 'lib:input',
            params: {
              selector: '[data-testid="dmComposerTextInput"]',
              value: '{{generate-reply}}',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'send-intelligent-reply',
            type: 'library',
            libraryId: 'lib:sendKeys',
            params: {
              keys: 'Enter',
              tabId: '{{open-twitter}}',
            },
          },
        ],
        {}
      );
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit intelligent Twitter DM reply failed:', error);
      setResult({
        success: false,
        error: `Intelligent Twitter DM reply failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  const runSimpleTwitterDMReply = async () => {
    try {
      setLoading('workflow:execute');
      const res = await sendWorkflowCommand(
        [
          {
            id: 'open-twitter',
            type: 'navigation',
            action: 'open',
            target: { url: 'https://twitter.com/messages' },
          },
          {
            id: 'wait-for-load',
            type: 'wait',
            duration: 3000,
          },
          {
            id: 'click-new-message',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="NewDM_Button"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'wait-for-compose',
            type: 'wait',
            duration: 2000,
          },
          {
            id: 'type-recipient',
            type: 'library',
            libraryId: 'lib:input',
            params: {
              selector: '[data-testid="searchPeople"]',
              value: '{{param.recipient}}',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'wait-for-search',
            type: 'wait',
            duration: 2000,
          },
          {
            id: 'select-user',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="TypeaheadUser"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'click-next',
            type: 'library',
            libraryId: 'lib:click',
            params: {
              selector: '[data-testid="nextButton"]',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'wait-for-chat',
            type: 'wait',
            duration: 2000,
          },
          {
            id: 'type-message',
            type: 'library',
            libraryId: 'lib:input',
            params: {
              selector: '[data-testid="dmComposerTextInput"]',
              value: '{{param.message}}',
              tabId: '{{open-twitter}}',
            },
          },
          {
            id: 'send-message',
            type: 'library',
            libraryId: 'lib:sendKeys',
            params: {
              keys: 'Enter',
              tabId: '{{open-twitter}}',
            },
          },
        ],
        {
          recipient: 'testuser',
          message: 'Hello! This is a test message from the workflow system.',
        }
      );
      setResult(res);
    } catch (error) {
      console.error('---[error] TestWebToolkit simple Twitter DM reply failed:', error);
      setResult({
        success: false,
        error: `Simple Twitter DM reply failed: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setLoading(null);
    }
  };

  return (
    <div style={{ height: '100vh', width: '100%', display: 'flex', flexDirection: 'column' }}>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          padding: 12,
          borderBottom: '1px solid #eee',
        }}
      >
        <button onClick={onBack} style={{ marginRight: 12 }}>
          ← Back
        </button>
        <div style={{ fontWeight: 600 }}>Test WebToolkit</div>
      </div>

      <div style={{ padding: 12, display: 'grid', gap: 12 }}>
        {/* Analyze / Basic */}
        <div>
          <button
            onClick={() => run('extractText')}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="webToolkit.summarize()"
          >
            {loading === 'extractText' ? 'Running…' : 'Extract Text'}
          </button>
        </div>

        <div style={{ display: 'grid', gap: 8 }}>
          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <input
              value={analyzeSelector}
              onChange={e => setAnalyzeSelector(e.target.value)}
              placeholder="selector (optional)"
              style={{
                maxWidth: 50,
                padding: '6px 8px',
                border: '1px solid #e5e7eb',
                borderRadius: 6,
              }}
            />
            <label style={{ display: 'inline-flex', alignItems: 'center', gap: 6 }}>
              <input
                type="checkbox"
                checked={generateDiff}
                onChange={e => setGenerateDiff(e.target.checked)}
              />
              Generate DOM Diff
            </label>
            <button
              onClick={() =>
                run('analyzePageDOM', {
                  selector: analyzeSelector || undefined,
                  generateDiff: generateDiff,
                })
              }
              disabled={loading !== null}
              style={{ padding: '8px 12px' }}
              title="webToolkit.buildDomTree()"
            >
              {loading === 'analyzePageDOM' ? 'Running…' : 'Analyze DOM'}
            </button>
          </div>
        </div>

        <div>
          <button
            onClick={() => run('buildDomTemplate')}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="webToolkit.buildDomTemplate()"
          >
            {loading === 'buildDomTemplate' ? 'Running…' : 'Build DOM Template'}
          </button>
        </div>

        <div>
          <button
            onClick={() => runWorkflowNodeCommand()}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="workflow:executeNode"
          >
            {loading === 'workflow:executeNode' ? 'Running…' : 'workflow:executeNode'}
          </button>
        </div>

        <div>
          <button
            onClick={() => runWorkflowCommand()}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="workflow:execute"
          >
            {loading === 'workflow:execute' ? 'Running…' : 'workflow:execute'}
          </button>
        </div>

        <div>
          <button
            onClick={() => runTwitterDMReplyWorkflow()}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="Twitter DM Reply Workflow"
          >
            {loading === 'workflow:execute' ? 'Running…' : 'Twitter DM Reply Workflow'}
          </button>
        </div>

        <div>
          <button
            onClick={() => runTwitterDMReplyWithCustomMessage()}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="Twitter DM With Custom Message"
          >
            {loading === 'workflow:execute' ? 'Running…' : 'Twitter DM With Custom Message'}
          </button>
        </div>

        <div>
          <button
            onClick={() => runLLMNodeCommand()}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="workflow:executeNode"
          >
            {loading === 'workflow:executeNode' ? 'Running…' : 'LLM Node Test'}
          </button>
        </div>

        <div>
          <button
            onClick={() => runLLMWorkflowCommand()}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="LLM Workflow"
          >
            {loading === 'workflow:execute' ? 'Running…' : 'LLM Workflow'}
          </button>
        </div>

        <div>
          <button
            onClick={() => runInputLibraryCommand()}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="Input Library"
          >
            {loading === 'workflow:execute' ? 'Running…' : 'Input Library'}
          </button>
        </div>

        <div>
          <button
            onClick={() => runSendKeysLibraryCommand()}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="SendKeys Library"
          >
            {loading === 'workflow:execute' ? 'Running…' : 'SendKeys Library'}
          </button>
        </div>

        <div>
          <button
            onClick={() => runIntelligentTwitterDMReply()}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="Intelligent Twitter DM Reply"
          >
            {loading === 'workflow:execute' ? 'Running…' : 'Intelligent Twitter DM Reply'}
          </button>
        </div>

        <div>
          <button
            onClick={() => runSimpleTwitterDMReply()}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="Simple Twitter DM Reply"
          >
            {loading === 'workflow:execute' ? 'Running…' : 'Simple Twitter DM Reply'}
          </button>
        </div>

        {/* Test Individual Library Nodes */}
        <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
          <button
            onClick={() => {
              setLoading('workflow:executeNode');
              sendWorkflowNodeCommand(`
                {
                  "id": "test-click",
                  "type": "library",
                  "libraryId": "lib:click",
                  "params": {
                    "selector": "button",
                    "tabId": 1
                  }
                }
              `)
                .then(setResult)
                .catch(error => {
                  setResult({
                    success: false,
                    error: `Click node test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                  });
                })
                .finally(() => setLoading(null));
            }}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="Test Click Node"
          >
            {loading === 'workflow:executeNode' ? 'Testing…' : 'Test Click Node'}
          </button>

          <button
            onClick={() => {
              setLoading('workflow:executeNode');
              sendWorkflowNodeCommand(`
                {
                  "id": "test-input",
                  "type": "library",
                  "libraryId": "lib:input",
                  "params": {
                    "selector": "input",
                    "value": "test input",
                    "tabId": 1
                  }
                }
              `)
                .then(setResult)
                .catch(error => {
                  setResult({
                    success: false,
                    error: `Input node test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                  });
                })
                .finally(() => setLoading(null));
            }}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="Test Input Node"
          >
            {loading === 'workflow:executeNode' ? 'Testing…' : 'Test Input Node'}
          </button>

          <button
            onClick={() => {
              setLoading('workflow:executeNode');
              sendWorkflowNodeCommand(`
                {
                  "id": "test-sendkeys",
                  "type": "library",
                  "libraryId": "lib:sendKeys",
                  "params": {
                    "keys": "Enter",
                    "tabId": 1
                  }
                }
              `)
                .then(setResult)
                .catch(error => {
                  setResult({
                    success: false,
                    error: `SendKeys node test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                  });
                })
                .finally(() => setLoading(null));
            }}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="Test SendKeys Node"
          >
            {loading === 'workflow:executeNode' ? 'Testing…' : 'Test SendKeys Node'}
          </button>
        </div>

        <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
          <input
            value={selectorOrIndex}
            onChange={e => setSelectorOrIndex(e.target.value)}
            placeholder="selector or index"
            style={{
              maxWidth: 50,
              padding: '6px 8px',
              border: '1px solid #e5e7eb',
              borderRadius: 6,
            }}
          />
          <button
            onClick={() => run('click', { selectorOrIndex })}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="webToolkit.click(selectorOrIndex)"
          >
            {loading === 'click' ? 'Running…' : 'Click Element'}
          </button>
        </div>

        <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
          <input
            value={selectorOrIndex}
            onChange={e => setSelectorOrIndex(e.target.value)}
            placeholder="selector or index"
            style={{
              maxWidth: 50,
              padding: '6px 8px',
              border: '1px solid #e5e7eb',
              borderRadius: 6,
            }}
          />
          <button
            onClick={() => run('scroll', { selectorOrIndex })}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="webToolkit.scroll(selectorOrIndex)"
          >
            {loading === 'scroll' ? 'Running…' : 'Scroll To Element'}
          </button>
        </div>

        <div style={{ fontSize: 12, color: '#6b7280' }}>
          Tips: For index-based click, try 0, 1, 2 …; for selector-based click, try a CSS selector
          like #id or .class
        </div>

        {/* Input */}
        <div style={{ display: 'grid', gap: 8 }}>
          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <input
              value={selectorOrIndex}
              onChange={e => setSelectorOrIndex(e.target.value)}
              placeholder="selector or index"
              style={{
                maxWidth: 50,
                padding: '6px 8px',
                border: '1px solid #e5e7eb',
                borderRadius: 6,
              }}
            />
            <input
              value={inputValue}
              onChange={e => setInputValue(e.target.value)}
              placeholder="value"
              style={{
                maxWidth: 50,
                padding: '6px 8px',
                border: '1px solid #e5e7eb',
                borderRadius: 6,
              }}
            />
            <button
              onClick={() =>
                run('input', {
                  selectorOrIndex,
                  value: inputValue,
                  clearFirst: inputClearFirst,
                  pressEnterAfterInput: inputPressEnter,
                })
              }
              disabled={loading !== null}
              style={{ padding: '8px 12px' }}
              title="webToolkit.input(selectorOrIndex, value, options)"
            >
              {loading === 'input' ? 'Running…' : 'Input Value'}
            </button>
          </div>
          <div style={{ display: 'flex', gap: 16, alignItems: 'center', fontSize: 12 }}>
            <label style={{ display: 'inline-flex', alignItems: 'center', gap: 6 }}>
              <input
                type="checkbox"
                checked={inputClearFirst}
                onChange={e => setInputClearFirst(e.target.checked)}
              />
              clearFirst
            </label>
            <label style={{ display: 'inline-flex', alignItems: 'center', gap: 6 }}>
              <input
                type="checkbox"
                checked={inputPressEnter}
                onChange={e => setInputPressEnter(e.target.checked)}
              />
              pressEnterAfterInput
            </label>
          </div>
        </div>

        {/* Send Keys */}
        <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
          <input
            value={keys}
            onChange={e => setKeys(e.target.value)}
            placeholder="keys (e.g. Escape, Enter, Control+Shift+T)"
            style={{
              maxWidth: 50,
              padding: '6px 8px',
              border: '1px solid #e5e7eb',
              borderRadius: 6,
            }}
          />
          <button
            onClick={() => run('sendKeys', { keys })}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="webToolkit.sendKeys({ keys })"
          >
            {loading === 'sendKeys' ? 'Running…' : 'Send Keys'}
          </button>
        </div>

        {/* Screenshot */}
        <div>
          <button
            onClick={() => run('screenshot')}
            disabled={loading !== null}
            style={{ padding: '8px 12px' }}
            title="webToolkit.screenshot()"
          >
            {loading === 'screenshot' ? 'Running…' : 'Screenshot'}
          </button>
        </div>

        {/* Refresh Page */}
        <div style={{ display: 'grid', gap: 8 }}>
          <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
            <input
              type="number"
              value={refreshTimeout}
              onChange={e => setRefreshTimeout(Number(e.target.value))}
              placeholder="timeout (ms)"
              style={{
                maxWidth: 50,
                padding: '6px 8px',
                border: '1px solid #e5e7eb',
                borderRadius: 6,
              }}
            />
            <button
              onClick={() =>
                run('refreshPage', {
                  timeout: refreshTimeout,
                })
              }
              disabled={loading !== null}
              style={{ padding: '8px 12px' }}
              title="webToolkit.refreshPage(url?, waitForLoad?, timeout?)"
            >
              {loading === 'refreshPage' ? 'Running…' : 'Refresh Page'}
            </button>
          </div>
        </div>

        <div
          style={{
            marginTop: 8,
            padding: 12,
            border: '1px solid #eee',
            borderRadius: 8,
            background: '#fafafa',
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: 6,
            }}
          >
            <div style={{ fontWeight: 600 }}>Result</div>
            <button
              onClick={async () => {
                try {
                  const text = JSON.stringify(result?.data ?? result, null, 2);
                  await navigator.clipboard.writeText(text ?? '');
                  setCopied(true);
                  setTimeout(() => setCopied(false), 1200);
                } catch {}
              }}
              title="Copy result"
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                gap: 6,
                padding: '4px 8px',
                borderRadius: 6,
                border: '1px solid #e5e7eb',
                background: '#ffffff',
                cursor: 'pointer',
                fontSize: 12,
                color: '#374151',
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
              </svg>
              {copied ? 'Copied' : 'Copy'}
            </button>
          </div>
          <pre style={{ margin: 0, whiteSpace: 'pre-wrap', wordBreak: 'break-word', fontSize: 12 }}>
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default TestWebToolkit;
