import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import TelegramRemoteConversation from '../components/remote/TelegramRemoteConversation';
import Header from '../components/Header';
import { useUser } from '~/hooks/useUser';

export const RemotePage: React.FC = () => {
  const navigate = useNavigate();
  const activeUser = useUser();
  const [showTelegramRemoteConversation, setShowTelegramRemoteConversation] = useState(false);

  const toggleTelegramRemoteConversation = (value?: boolean) => {
    const willShow = value !== undefined ? value : !showTelegramRemoteConversation;
    setShowTelegramRemoteConversation(willShow);
  };

  return (
    <div
      style={{
        height: '100vh',
        width: '100%',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Header */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          backgroundColor: 'white',
          zIndex: 10,
        }}
      >
        <Header
          setShowConversationList={() => {}} // Not used in RemotePage
          setShowTelegramRemoteConversation={toggleTelegramRemoteConversation}
          user={activeUser}
        />
      </div>

      {/* Main Content */}
      <div
        style={{
          position: 'absolute',
          top: '44px',
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: '#ffffff',
          overflow: 'hidden',
        }}
      >
        <TelegramRemoteConversation
          onBack={() => {
            navigate(-1);
          }}
        />
      </div>

      {/* Telegram Remote Conversation List */}
      {showTelegramRemoteConversation && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 50,
            animation: 'fadeIn 0.2s ease-out',
          }}
          onClick={() => setShowTelegramRemoteConversation(false)}
        >
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '320px',
              height: '100%',
              backgroundColor: '#ffffff',
              boxShadow: '2px 0 10px rgba(0, 0, 0, 0.1)',
              animation: 'slideInFromLeft 0.3s ease-out',
              overflow: 'hidden',
            }}
            onClick={e => e.stopPropagation()}
          >
            <TelegramRemoteConversation
              onBack={() => {
                setShowTelegramRemoteConversation(false);
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};
