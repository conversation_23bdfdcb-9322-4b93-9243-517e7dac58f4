import React from 'react';
import { X } from 'lucide-react';
import { Tooltip } from 'antd';
import { useLanguage } from '~/utils/i18n';

interface RemoteConversationListProps {
  onClose: () => void;
}

const RemoteConversationList: React.FC<RemoteConversationListProps> = ({ onClose }) => {
  const { getMessage } = useLanguage();

  return (
    <div 
      style={{ 
        width: '100%', 
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        backgroundColor: '#ffffff',
        borderRadius: '0 16px 16px 0',
        boxShadow: '0 0 15px 0 rgba(0, 0, 0, 0.15)',
        overflow: 'hidden',
      }}
    >
      {/* Header */}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '0 16px',
          height: '44px',
          borderBottom: '1px solid #f0f0f0',
        }}
      >
        <h2 style={{ fontSize: '15px', fontWeight: 600, color: '#111827' }}>
          {getMessage('remoteConversation')}
        </h2>
        <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
          <Tooltip title={getMessage('tooltipClose')} placement="bottom">
            <button
              onClick={onClose}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                backgroundColor: 'transparent',
                border: 'none',
                color: '#6b7280',
                cursor: 'pointer',
                transition: 'background 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#E5E7EB';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <X color="#374151" size={20} />
            </button>
          </Tooltip>
        </div>
      </div>

      {/* Content */}
      <div
        style={{
          flex: 1,
          overflowY: 'auto',
          padding: '16px',
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: 'calc(100vh - 200px)',
          }}
        >
          <p style={{ fontSize: '14px', color: '#6b7280' }}>
            {getMessage('noRemoteConversations') || 'No remote conversations'}
          </p>
        </div>
      </div>
    </div>
  );
};

export default RemoteConversationList;
