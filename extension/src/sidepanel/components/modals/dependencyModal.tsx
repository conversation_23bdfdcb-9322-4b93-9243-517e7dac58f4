import React, { useState } from 'react';
import { Modal } from 'antd';
import { X } from 'lucide-react';
import inputIcon from '~/assets/icons/input.svg';
import clickIcon from '~/assets/icons/click.svg';
import sendkeysIcon from '~/assets/icons/sendkeys.svg';

interface DependencyModalProps {
  open: boolean;
  onClose: () => void;
  onSelectionChange?: (hasSelectedItems: boolean) => void;
}

interface LibraryItem {
  id: string;
  name: string;
  icon?: string;
  type: 'preset' | 'custom';
  description?: string;
}

const DependencyModal: React.FC<DependencyModalProps> = ({ open, onClose, onSelectionChange }) => {
  // Preset library items - only show first 3 items
  const presetItems: LibraryItem[] = [
    { id: 'input', name: 'Input', icon: inputIcon, type: 'preset' },
    { id: 'click', name: 'Click', icon: clickIcon, type: 'preset' },
    { id: 'sendkeys', name: 'Send<PERSON><PERSON><PERSON>', icon: sendkeysIcon, type: 'preset' },
  ];

  // Mock data for My Library
  const myLibraryItems: LibraryItem[] = [
    { id: 'login', name: 'Log in to website and post a new message', type: 'custom' },
    { id: 'summarize1', name: 'Summarize the key points of the webpage', type: 'custom' },
    { id: 'summarize2', name: 'Summarize the key points of the webpage', type: 'custom' },
    { id: 'login2', name: 'Log in to website and post a new message', type: 'custom' },
    { id: 'login3', name: 'Log in to website and post a new message', type: 'custom' },
  ];

  // Selection state - no default selections
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());

  const toggleSelection = (itemId: string) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId);
    } else {
      newSelected.add(itemId);
    }
    setSelectedItems(newSelected);

    // Notify parent about selection change
    if (onSelectionChange) {
      onSelectionChange(newSelected.size > 0);
    }
  };

  const resetSelection = () => {
    setSelectedItems(new Set());

    // Notify parent about selection change
    if (onSelectionChange) {
      onSelectionChange(false);
    }
  };

  const getAvatarColor = (name: string) => {
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
    const index = name.length % colors.length;
    return colors[index];
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 1);
  };

  const renderPresetItem = (item: LibraryItem) => {
    const isSelected = selectedItems.has(item.id);
    return (
      <div
        key={item.id}
        onClick={() => toggleSelection(item.id)}
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '16px 12px',
          borderRadius: '8px',
          border: isSelected ? '1px solid transparent' : '1px solid #E5E7EB',
          background: isSelected
            ? 'linear-gradient(#F9F9F9, #F9F9F9) padding-box, linear-gradient(90deg, #723DF6, #0081E4) border-box'
            : '#FFFFFF',
          cursor: 'pointer',
          transition: 'all 0.2s',
          aspectRatio: '1',
          width: '100%',
          boxSizing: 'border-box',
          gap: '10px',
        }}
      >
        {item.icon ? (
          <img src={item.icon} alt={item.name} style={{ width: 28, height: 28, flexShrink: 0 }} />
        ) : (
          <div
            style={{
              width: 28,
              height: 28,
              borderRadius: '6px',
              backgroundColor: '#9CA3AF',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '12px',
              color: '#FFFFFF',
              fontWeight: 600,
              flexShrink: 0,
            }}
          >
            {getInitials(item.name)}
          </div>
        )}
        <span
          style={{
            fontSize: '13px',
            fontWeight: 500,
            color: '#111827',
            textAlign: 'center',
            lineHeight: '1.2',
            wordBreak: 'break-word',
            overflow: 'hidden',
          }}
        >
          {item.name}
        </span>
      </div>
    );
  };

  const renderMyLibraryItem = (item: LibraryItem) => {
    const isSelected = selectedItems.has(item.id);
    return (
      <div
        key={item.id}
        onClick={() => toggleSelection(item.id)}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          padding: '12px',
          borderRadius: '8px',
          border: isSelected ? '1px solid transparent' : '1px solid #E5E7EB',
          background: isSelected
            ? 'linear-gradient(#F9F9F9, #F9F9F9) padding-box, linear-gradient(90deg, #723DF6, #0081E4) border-box'
            : '#FFFFFF',
          cursor: 'pointer',
          transition: 'all 0.2s',
          marginBottom: '8px',
        }}
      >
        <div
          style={{
            width: '32px',
            height: '32px',
            borderRadius: '6px',
            backgroundColor: getAvatarColor(item.name),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#FFFFFF',
            fontSize: '12px',
            fontWeight: 600,
            flexShrink: 0,
          }}
        >
          {getInitials(item.name)}
        </div>
        <span
          style={{
            fontSize: '14px',
            fontWeight: 400,
            color: '#111827',
            flex: 1,
          }}
        >
          {item.name}
        </span>
      </div>
    );
  };

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      closable={false}
      width="100%"
      styles={{
        body: {
          padding: 0,
          height: '75vh',
          display: 'flex',
          flexDirection: 'column',
        },
        content: {
          borderRadius: '16px 16px 0 0',
          overflow: 'hidden',
          margin: 0,
          padding: 0,
        },
        mask: {
          backgroundColor: 'rgba(0, 0, 0, 0.45)',
        },
      }}
      style={{
        top: '25vh',
        margin: 0,
        maxWidth: '100%',
        paddingBottom: 0,
      }}
    >
      <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        {/* Fixed Header */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '12px 16px',
            borderBottom: '1px solid #F0F0F0',
            backgroundColor: '#FFFFFF',
          }}
        >
          <h2
            style={{
              margin: 0,
              fontSize: '16px',
              fontWeight: 600,
              color: '#111827',
            }}
          >
            Workflow project as dependency
          </h2>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              padding: '4px',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <X size={18} color="#6B7280" />
          </button>
        </div>

        {/* Selected Count and Reset */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '12px 16px',
            backgroundColor: '#FFFFFF',
          }}
        >
          <span
            style={{
              fontSize: '14px',
              fontWeight: 500,
              color: '#111827',
            }}
          >
            Selected: {selectedItems.size}
          </span>
          <button
            onClick={resetSelection}
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 500,
              color: '#6B7280',
              textDecoration: 'underline',
            }}
          >
            Reset
          </button>
        </div>

        {/* Scrollable Content */}
        <div
          style={{
            flex: 1,
            overflowY: 'auto',
            padding: '16px',
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
          }}
        >
          {/* Preset Library - 3 items in a row */}
          <div style={{ marginBottom: '24px' }}>
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr 1fr',
                gap: '12px',
                width: '100%',
                boxSizing: 'border-box',
              }}
            >
              {presetItems.map(renderPresetItem)}
            </div>
          </div>

          {/* My Library */}
          <div>{myLibraryItems.map(renderMyLibraryItem)}</div>
        </div>
      </div>
    </Modal>
  );
};

export default DependencyModal;
