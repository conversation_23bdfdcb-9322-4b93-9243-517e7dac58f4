import { ChatStatus, Conversation, Message } from '@the-agent/shared';
import { Dropdown, Tag, Tooltip } from 'antd';
import { UserIcon, X } from 'lucide-react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import exitIcon from '~/assets/icons/exit.svg';
import switchIcon from '~/assets/icons/switch.svg';
import connectionError from '~/assets/imgs/remote-connection-error.svg';
import emptyPlaceholder from '~/assets/imgs/remote-message-empty.svg';
import { ChatHandler } from '~/chat/handler';
import useDebouncedEffect from '~/hooks/useDebouncedEffect';
import useInterval from '~/hooks/useInterval';
import { useUser } from '~/hooks/useUser';
import { createNewConversationWithType } from '~/services/conversation';
import {
  addMessageListener,
  getBotStatus,
  getTelegramBotClient,
  removeMessageListener,
} from '~/services/gramjs';
import { db } from '~/storages/indexdb';
import { useLocalBotParams, useTelegramBotStore } from '~/stores/telegramBotStore';
import { useTelegramStore } from '~/stores/telegramStore';
import { getMessage } from '~/utils/i18n';
import { stringToNumber } from '~/utils/string';
import { Loading } from '../Loading';
import MessageList from '../MessageList';
import ConfirmModal from '../modals/ConfirmModal';

interface TelegramMessage {
  text: string;
  from: string;
  timestamp: number;
  chatId: string;
}

interface BotStatus {
  isPolling: boolean;
  botUsername: string;
}

interface RemoteConversationProps {
  onBack: () => void;
}

const workflowMode = false;

// Utility function to check for FloodWaitError and extract wait time
const parseFloodWaitError = (
  errorMessage: string
): { isFloodWait: boolean; waitSeconds?: number } => {
  // A wait of 2489 seconds is required (caused by auth.ImportBotAuthorization)
  const floodWaitPattern = /A wait of (\d+)\s*seconds/;
  const match = errorMessage.match(floodWaitPattern);

  if (match) {
    return {
      isFloodWait: true,
      waitSeconds: parseInt(match[1], 10),
    };
  }

  return { isFloodWait: false };
};

export const RemoteSingleConversation: React.FC<RemoteConversationProps> = ({ onBack }) => {
  const [botStatus, setBotStatus] = useState<BotStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [exitConfirmModalOpen, setExitConfirmModalOpen] = useState(false);
  const [logoutConfirmModalOpen, setLogoutConfirmModalOpen] = useState(false);
  const [status, setStatus] = useState<ChatStatus>('idle');
  const [chatHandler, setChatHandler] = useState<ChatHandler | null>(null);
  const [conversation, setConversation] = useState<Conversation | null>(null);

  const [connectionError, setConnectionError] = useState(false);
  const [reconnectEnableTime, setReconnectEnableTime] = useState(0);

  const activeUser = useUser();
  const { logoutTelegram, signInTelegram } = useTelegramStore();
  const { setLocalBotParams, logoutTelegramBot } = useTelegramBotStore();
  const localBotParams = useLocalBotParams();
  const messageHandlerRef = useRef<any>(null);
  const chatHandlerRef = useRef<ChatHandler | null>(null);
  const statusRef = useRef<ChatStatus>('idle');

  useEffect(() => {
    chatHandlerRef.current = chatHandler;
  }, [chatHandler]);

  useEffect(() => {
    statusRef.current = status;
  }, [status]);

  const handleIncomingMessage = useCallback((newMessage: TelegramMessage) => {
    if (statusRef.current !== 'idle') {
      const telegramBotClient = getTelegramBotClient();
      telegramBotClient.sendMessage(replyPeerId || '', {
        message: `Please try again after current task finishes.`,
      });
      return;
    }

    console.log('handleIncomingMessage', newMessage);
    if (chatHandlerRef.current) {
      console.log('handleSubmit', newMessage.text);
      chatHandlerRef.current.handleSubmit(newMessage.text);
    }
  }, []);

  const convId = useMemo(() => {
    if (!localBotParams) {
      return null;
    }
    return stringToNumber(
      localBotParams.token +
        (localBotParams.targetUserId || '') +
        (localBotParams.groupChatId || '')
    );
  }, [localBotParams]);

  const replyPeerId = localBotParams?.groupChatId || localBotParams?.targetUserId;

  const loadConversation = useCallback(async () => {
    if (!convId) {
      return;
    }
    const conversation = await db.getConversation(convId);
    setConversation(conversation);
  }, [convId]);

  useEffect(() => {
    loadConversation();
  }, [loadConversation]);

  useDebouncedEffect(
    () => {
      (async () => {
        if (!botStatus?.isPolling || !botStatus?.botUsername || !localBotParams) {
          return;
        }
        if (activeUser?.id && !conversation?.id) {
          const convId = stringToNumber(
            localBotParams.token +
              (localBotParams.targetUserId || '') +
              (localBotParams.groupChatId || '')
          );

          await createNewConversationWithType(
            activeUser.id,
            convId,
            'remote',
            `Remote Conversation with @${botStatus?.botUsername}`
          );
          loadConversation();
        }
      })();
    },
    [
      conversation?.id,
      activeUser?.id,
      botStatus?.isPolling,
      botStatus?.botUsername,
      localBotParams,
    ],
    1000
  );

  useEffect(() => {
    console.log('useEffect', activeUser?.id, conversation?.id);
    if (activeUser?.id && conversation?.id) {
      setChatHandler(
        new ChatHandler({
          currentConversationId: conversation.id,
          conversationType: 'remote',
          setStatus,
          onMessageComplete: async (message: Message) => {
            const telegramBotClient = getTelegramBotClient();
            if (message.role === 'user') {
              return;
            }

            if (message.reasoning) {
              telegramBotClient.sendMessage(replyPeerId || '', {
                message: message.reasoning,
              });
            }

            if (message.role === 'assistant') {
              const text = message.content || '';
              if (text) {
                telegramBotClient.sendMessage(replyPeerId || '', {
                  message: text,
                });
              }
            }
            if (message.role === 'tool') {
              telegramBotClient.sendMessage(replyPeerId || '', {
                message: `<b>🔧 Tool call:</b> <code>${message.name?.replace('WebToolkit_', '')}</code>`,
                parseMode: 'html',
              });
            }
            if (message.status === 'error') {
              telegramBotClient.sendMessage(replyPeerId || '', {
                message: `<b>🚨 Error:</b> <code>${message.error ?? 'unknown error'}</code>`,
                parseMode: 'html',
              });
            }
          },
        })
      );
    }
  }, [activeUser?.id, conversation?.id]);

  useDebouncedEffect(
    () => {
      if (localBotParams?.token) {
        handleStartBot();
      }
    },
    [localBotParams],
    200
  );

  const handleStartBot = async () => {
    setLoading(true);
    setConnectionError(false);
    setReconnectEnableTime(0);
    try {
      const currentBotStatus = await getBotStatus();
      if (currentBotStatus?.isPolling) {
        setBotStatus(currentBotStatus);
        return;
      }

      const botClient = getTelegramBotClient();

      await botClient.start({
        botAuthToken: localBotParams!.token,
      });

      // Add message listener
      const handler = addMessageListener(
        handleIncomingMessage,
        error => {
          console.error('====bot error====', error);
          setConnectionError(true);
        },
        localBotParams?.groupChatId,
        localBotParams?.targetUserId
      );
      messageHandlerRef.current = handler;

      // Update bot status
      await updateBotStatus();
    } catch (error: any) {
      console.error('=====start bot error====', error);
      // Check if it's a FloodWaitError
      const errorMessage = error.message || error.toString();
      const floodWaitInfo = parseFloodWaitError(errorMessage);
      if (floodWaitInfo.isFloodWait) {
        setReconnectEnableTime((floodWaitInfo.waitSeconds || 0) + Date.now() / 1000);
      }
      setConnectionError(true);
    } finally {
      setLoading(false);
    }
  };

  const updateBotStatus = async () => {
    try {
      if (loading) {
        return;
      }
      if (connectionError) {
        setBotStatus(null);
        return;
      }
      const status = await getBotStatus();
      console.log('====bot status====', status);
      if (status) {
        setBotStatus({
          ...status,
        });
      } else {
        setBotStatus(null);
        setConnectionError(true);
      }
    } catch (error) {
      console.error('Error getting bot status:', error);
    }
  };

  const getStatusColor = (isPolling: boolean) => {
    return isPolling ? 'green' : 'red';
  };

  const getStatusText = (isPolling: boolean) => {
    return isPolling ? getMessage('active') : getMessage('inactive');
  };

  // Cleanup effect
  useEffect(() => {
    return () => {
      // Cleanup message listener when component unmounts
      if (messageHandlerRef.current) {
        removeMessageListener(messageHandlerRef.current);
        messageHandlerRef.current = null;
      }
    };
  }, []);

  // Effect to check bot status periodically
  useEffect(() => {
    if (!connectionError && botStatus?.isPolling) {
      const interval = setInterval(() => {
        updateBotStatus();
      }, 10000); // Check every 10 seconds

      return () => clearInterval(interval);
    }
  }, [botStatus?.isPolling, connectionError]);

  const handleRefresh = async () => {
    await updateBotStatus();
  };

  useInterval(() => {
    handleRefresh();
  }, 10000);

  const handleStopBot = (stopTelegramBot: boolean = true) => {
    // Remove message listener
    if (messageHandlerRef.current) {
      removeMessageListener(messageHandlerRef.current);
      messageHandlerRef.current = null;
    }

    setBotStatus(null);
    if (stopTelegramBot) {
      logoutTelegramBot();
    }
  };

  const handleExit = () => {
    handleStopBot(false);
    onBack();
  };

  const handleLogoutTelegram = async () => {
    handleStopBot();
    setLocalBotParams(null);
    await logoutTelegram();
    await signInTelegram();
  };

  const dropdownMenu = (
    <div
      style={{
        minWidth: 180,
        background: '#fff',
        borderRadius: 18,
        boxShadow: '0 2px 12px 0 rgba(0,0,0,0.30)',
        padding: '18px 18px 12px 18px',
        display: 'flex',
        flexDirection: 'column',
        gap: 12,
        fontSize: 12,
        fontWeight: 500,
      }}
    >
      {botStatus?.isPolling && botStatus?.botUsername && (
        <div style={{ opacity: 0.5, fontSize: 12 }}>@{botStatus?.botUsername}</div>
      )}

      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'start',
          gap: 8,
          cursor: 'pointer',
        }}
        onClick={async () => {
          setLogoutConfirmModalOpen(true);
        }}
      >
        <img src={exitIcon} alt="exit" style={{ width: 14, height: 14 }} />

        <div>{getMessage('logoutTelegram')}</div>
      </div>

      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'start',
          gap: 8,
          cursor: 'pointer',
        }}
        onClick={async () => {
          setLocalBotParams(null);
          await logoutTelegramBot();
          await signInTelegram();
        }}
      >
        <img src={switchIcon} alt="exit" style={{ width: 14, height: 14 }} />

        <div>{getMessage('switchBot')}</div>
      </div>
    </div>
  );

  if (connectionError) {
    return (
      <div
        style={{
          height: '100vh',
          padding: '12px 12px 60px 12px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'stretch',
          boxSizing: 'border-box',
        }}
      >
        <div
          style={{
            flex: 1,
            border: '1px solid #00000010',
            borderRadius: '15px',
            position: 'relative',
            padding: '0px 12px',
          }}
        >
          <div
            style={{
              height: '50px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-end',
              borderBottom: '1px solid #00000010',
              padding: '0 16px',
              position: 'relative',
            }}
          >
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: 4,
                position: 'absolute',
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)',
                fontSize: 16,
                fontWeight: 600,
              }}
            >
              Remote
            </div>

            <X
              size={18}
              style={{
                cursor: 'pointer',
                top: 16,
              }}
              onClick={handleExit}
            />
          </div>

          <ConnectionError
            onRetry={handleStartBot}
            reconnectEnableTime={reconnectEnableTime}
            onSwitchBot={() => {
              setLocalBotParams(null);
              logoutTelegramBot();
              signInTelegram();
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <div
      style={{
        height: '100vh',
        padding: '12px 12px 64px 12px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'stretch',
        boxSizing: 'border-box',
      }}
    >
      <div
        style={{
          flex: 1,
          border: '1px solid #00000010',
          borderRadius: '15px',
          position: 'relative',
        }}
      >
        <div
          style={{
            height: '50px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
            borderBottom: '1px solid #00000010',
            padding: '0 16px',
            position: 'relative',
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: 4,
              position: 'absolute',
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)',
            }}
          >
            <Tag color={getStatusColor(botStatus?.isPolling || false)}>
              {getMessage('remote')}: {getStatusText(botStatus?.isPolling || false)}
            </Tag>
          </div>

          <Dropdown popupRender={() => dropdownMenu} trigger={['click']} placement="bottomRight">
            <Tooltip title={getMessage('remoteConversation')} placement="bottom">
              <button
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: 'none',
                  borderRadius: '50%',
                  padding: 0,
                  cursor: 'pointer',
                  width: 32,
                  height: 32,
                  backgroundColor: 'transparent',
                  transition: 'background 0.2s',
                }}
                onMouseOver={e => {
                  e.currentTarget.style.backgroundColor = '#E5E7EB';
                }}
                onMouseOut={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                <UserIcon color="#374151" size={20} />
              </button>
            </Tooltip>
          </Dropdown>
        </div>

        {/* Content */}
        <div
          style={{
            flex: 1,
            padding: '20px',
            overflow: 'auto',
          }}
        >
          {!loading && botStatus?.isPolling ? (
            // Bot is running - show status and messages
            <>
              {/* Messages List */}
              <MessageList
                convId={conversation?.id ?? -1}
                workflowMode={workflowMode}
                welcomeComponent={<EmptyPlaceHolder botUsername={botStatus?.botUsername || ''} />}
                status={status}
                hasBanner={false}
                top={60}
                bottom={4}
              />
            </>
          ) : (
            // Bot is not running - show instructions and start button
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '40px 20px',
                textAlign: 'center',
              }}
            >
              <Loading />
            </div>
          )}
        </div>
      </div>

      <div
        style={{
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          padding: 12,
          backgroundColor: '#fff',
        }}
      >
        <div
          style={{
            padding: '12px',
            borderRadius: '8px',
            backgroundColor: '#333',
            cursor: 'pointer',
            color: '#fff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          onClick={() => {
            setExitConfirmModalOpen(true);
          }}
        >
          {getMessage('exitRemoteMode')}
        </div>
      </div>

      <ConfirmModal
        open={exitConfirmModalOpen}
        onClose={() => setExitConfirmModalOpen(false)}
        onConfirm={handleExit}
        title={getMessage('exitRemoteMode')}
        content={getMessage('exitRemoteModeConfirm')}
      />

      <ConfirmModal
        open={logoutConfirmModalOpen}
        onClose={() => setLogoutConfirmModalOpen(false)}
        onConfirm={handleLogoutTelegram}
        title={getMessage('logoutTelegram')}
        content={getMessage('logoutTelegramConfirm')}
      />
    </div>
  );
};

const EmptyPlaceHolder = ({ botUsername }: { botUsername: string }) => {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        paddingTop: '120px',
      }}
    >
      <img src={emptyPlaceholder} alt="empty" style={{ width: 160, height: 160 }} />

      <div
        style={{
          marginTop: '40px',
          fontSize: 18,
          fontWeight: 500,
          textAlign: 'center',
        }}
      >
        {getMessage('noMessagesReceivedYet', [botUsername])}
      </div>
    </div>
  );
};

const ConnectionError = ({
  onRetry,
  onSwitchBot,
  reconnectEnableTime,
}: {
  onRetry: () => void;
  onSwitchBot: () => void;
  reconnectEnableTime: number;
}) => {
  const [timeLeft, setTimeLeft] = useState(0);

  useInterval(() => {
    setTimeLeft(Math.max(0, Math.floor(reconnectEnableTime - Date.now() / 1000)));
  }, 1000);

  const formatTime = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        paddingTop: '120px',
      }}
    >
      <img src={connectionError} alt="error" style={{ width: 160, height: 140 }} />

      <div
        style={{
          marginTop: '40px',
          fontSize: 18,
          fontWeight: 500,
          textAlign: 'center',
        }}
      >
        {getMessage('cannotStartBot')}
      </div>

      <div
        style={{
          alignSelf: 'stretch',
          marginTop: '70px',
        }}
      >
        <div
          style={{
            padding: '12px',
            borderRadius: '8px',
            backgroundColor: '#333',
            color: '#fff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: timeLeft > 0 ? 'not-allowed' : 'pointer',
            opacity: timeLeft > 0 ? 0.5 : 1,
          }}
          onClick={() => {
            if (timeLeft > 0) {
              return;
            }
            onRetry();
          }}
        >
          {timeLeft > 0 ? getMessage('retryWithTime', [formatTime(timeLeft)]) : getMessage('retry')}
        </div>
      </div>

      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'start',
          gap: 8,
          cursor: 'pointer',
          marginTop: '12px',
        }}
        onClick={onSwitchBot}
      >
        <div
          style={{
            textDecoration: 'underline',
          }}
        >
          {getMessage('switchBot')}
        </div>
      </div>
    </div>
  );
};
