import { db } from '~/storages/indexdb';
import { createApiClient } from './api/client';
import { Template, TemplateRegisterRequest } from '@the-agent/shared';
import { executeToolInBackground } from '~/utils/toolkit';

export const syncTemplates = async (): Promise<void> => {
  try {
    const client = await createApiClient();
    const response = await client.getTemplates({});
    await db.saveTemplates(response.templates);
  } catch (error) {
    console.error('Error in syncConversations:', JSON.stringify(error, null, 2));
    throw error;
  }
};

export async function getTemplateId(): Promise<number> {
  try {
    const template = await db.getTemplate(window.location.href);
    if (template) {
      return template.id;
    }
    // register new template
    const { success, data, error } = await executeToolInBackground(
      'WebToolkit_buildDomTemplate',
      {}
    );
    if (!success) {
      throw new Error('Failed to build dom template with error: ' + (error ?? data ?? 'unknown'));
    }
    const client = await createApiClient();
    const res = await client.registerTemplate(data as TemplateRegisterRequest);
    await db.saveTemplate({
      id: res.id,
      domain: new URL(window.location.href).hostname,
      path_pattern: res.path_pattern,
    } as Template);
    return res.id;
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : 'Unknown error');
  }
}
