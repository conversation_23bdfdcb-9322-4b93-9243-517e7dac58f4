import {
  APIClient,
  ContextBuilder,
  MemoryOptions,
  Message,
  SearchMemoryResponse,
  MemoryItem,
  SearchMemoryRequestV2,
  ContextBuilderConfig,
} from '@the-agent/shared';
import {
  buildContextMessage,
  buildMemoryContextChunk,
  buildProceduralMemoryContextChunk,
  getValidRecentMessages,
} from '~/utils/context';
import { ContextChunk } from '~/types';
import { db } from '~/storages/indexdb';

const DEFAULT_MEMORY_OPTIONS: MemoryOptions = {
  recent: 5,
  related: 5,
  site: 5,
  graph: 0,
  tab: true,
};

export class DefaultContextBuilder implements ContextBuilder {
  private apiClient: APIClient;

  constructor(apiClient: APIClient) {
    this.apiClient = apiClient;
  }

  async build(message: Message, config?: ContextBuilderConfig): Promise<Message[]> {
    const memoryOptions = { ...DEFAULT_MEMORY_OPTIONS, ...(config?.memoryOptions ?? {}) };
    const recentMessages = await getValidRecentMessages(message, memoryOptions.recent);
    const chunks = await this.buildContextChunks(message, memoryOptions);
    const contextMessage = buildContextMessage(chunks, message);
    return [...recentMessages, contextMessage];
  }

  private async buildContextChunks(
    message: Message,
    memoryOptions: MemoryOptions
  ): Promise<ContextChunk[]> {
    const relatedMemories = await this.searchMemory(message, memoryOptions.related);
    const semanticMemories = relatedMemories.results.filter(
      (mem: MemoryItem) => mem.metadata?.memoryType === 'semantic'
    );
    const proceduralMemories = relatedMemories.results.filter(
      (mem: MemoryItem) => mem.metadata?.memoryType === 'procedural'
    );
    return [
      buildMemoryContextChunk(semanticMemories),
      buildProceduralMemoryContextChunk(proceduralMemories),
    ].filter(chunk => chunk !== undefined);
  }

  private async searchMemory(message: Message, limit: number = 3): Promise<SearchMemoryResponse> {
    if (!message.content || limit <= 0) {
      return {
        results: [],
        relations: [],
      };
    }

    const newFilters: SearchMemoryRequestV2['config']['filters'] = {};

    if (message.conversation_id) {
      newFilters.conversationId = message.conversation_id.toString();
    }

    // For procedural memory search, include all available filters
    if (message.agent_id) {
      newFilters.agentId = message.agent_id.toString();
    }

    if (message.task_id) {
      newFilters.taskId = message.task_id.toString();
    }

    if (message.run_id) {
      newFilters.runId = message.run_id.toString();
    }

    return await this.apiClient.searchMemoryV2({
      text: message.content || '',
      config: {
        limit,
        filters: newFilters,
      },
    });
  }
}

export class MiniappContextBuilder extends DefaultContextBuilder {
  async build(message: Message, config?: ContextBuilderConfig): Promise<Message[]> {
    const results = await super.build(message, config);
    const convId = message.conversation_id;
    const miniapp = await db.getMiniappByConversationId(convId);
    if (!miniapp) {
      return results;
    }

    const code = miniapp.developing?.code ?? miniapp.installation?.code;
    if (!code) {
      return results;
    }

    results.push(
      buildContextMessage(
        [
          {
            title: 'Latest Script Version:',
            content: `\`\`\`${code}\`\`\``,
          },
        ],
        message
      )
    );
    return results;
  }
}
