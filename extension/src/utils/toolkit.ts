import { Too<PERSON><PERSON>all, Too<PERSON><PERSON>allR<PERSON>ult, ToolDescription, WebContext } from '@the-agent/shared';

export function parseToolParams(toolCall: ToolCall): unknown {
  try {
    return toolCall.function.arguments ? JSON.parse(toolCall.function.arguments) : {};
  } catch (error) {
    console.error('Error parsing tool arguments:', error);
    return {};
  }
}

export async function executeToolInBackground(name: string, params: any): Promise<ToolCallResult> {
  const message = {
    name: 'execute-tool',
    body: { name, arguments: params },
  };

  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage(message, response => {
      if (chrome.runtime.lastError) {
        console.error('Background error:', chrome.runtime.lastError);
        reject(chrome.runtime.lastError);
        return;
      }

      if (!response) {
        const error = new Error('No response received from background script');
        console.error(error);
        reject(error);
        return;
      }

      if (!response.success) {
        const error = new Error(response.error || 'Unknown error');
        console.error('Tool execution failed:', error);
        reject(error);
        return;
      }

      resolve(response);
    });
  });
}

export async function refreshThePage() {
  await executeToolInBackground('WebToolkit_refreshPage', { timeout: 5000 });
}

export async function getWebContext(): Promise<WebContext | null> {
  return await new Promise<WebContext | null>((resolve, reject) => {
    chrome.runtime.sendMessage(
      {
        name: 'get-web-context',
      },
      response => {
        if (response?.success) {
          resolve(response.data as WebContext | null);
        } else {
          console.error('Failed to get web context:', response?.error);
          reject(new Error(response?.error || 'Failed to get web context'));
        }
      }
    );
  });
}

export function isToolScopeMatch(
  tool: ToolDescription,
  agentId: string,
  webctx?: WebContext | null
): boolean {
  return (
    tool.scope.agents.includes(agentId) &&
    matchUrlPatterns(tool.scope.urlPatterns ?? [], webctx?.url)
  );
}

function matchUrlPatterns(patterns: string[], url?: string | null): boolean {
  if (patterns.length === 0) {
    return false;
  }
  const normalizedUrl = url?.trim().toLowerCase();
  return patterns.some(pattern => matchUrlPattern(pattern, normalizedUrl));
}

function matchUrlPattern(pattern: string, url?: string | null): boolean {
  if (pattern === '<all_urls>' || pattern === url) {
    return true;
  } else if (!url) {
    return false;
  } else {
    return new RegExp(pattern).test(url);
  }
}
