import { MemoryItem, MemoryRelation, Message } from '@the-agent/shared';
import { db } from '~/storages/indexdb';
import { ContextChunk } from '~/types';

/**
 * Builds a context chunk from a list of memories.
 * @param memories - semantic memories, procedural memories
 * @returns A context chunk containing the memories.
 */
export function buildMemoryContextChunk(memories: MemoryItem[]): ContextChunk | undefined {
  if (!memories || memories.length === 0) {
    return undefined;
  }

  const memoryContext = memories.map(mem => mem.memory).join('\n\n');

  return {
    title: 'Related memories from previous conversations',
    content: memoryContext,
  };
}

export function buildProceduralMemoryContextChunk(
  memories: MemoryItem[]
): ContextChunk | undefined {
  if (!memories || memories.length === 0) {
    return undefined;
  }

  // filter out procedural memories
  const proceduralMemories = memories.filter(mem => mem.metadata?.memoryType === 'procedural');

  if (proceduralMemories.length === 0) {
    return undefined;
  }

  const proceduralContext = proceduralMemories.map(mem => mem.memory).join('\n\n');
  return {
    title: 'Task Execution History',
    content: proceduralContext,
  };
}

export function buildGraphContextPrompt(
  graphRelations: MemoryRelation[] = []
): ContextChunk | undefined {
  if (graphRelations.length === 0) {
    return undefined;
  }
  let contextPrompt = '';
  for (const rel of graphRelations) {
    const src = rel.source;
    const dst = rel.destination;
    const relType = rel.relationship;
    contextPrompt += `- ${src} --[${relType}]--> ${dst}\n`;
  }
  return {
    title: 'Knowledge Graph Relations',
    content: contextPrompt,
  };
}

export function buildContextMessage(chunks: ContextChunk[], message: Message): Message {
  const prompt = chunks
    .map(chunk => {
      const title = chunk.title ? `## ${chunk.title}\n` : '';
      const footer = chunk.footer ? `\n${chunk.footer}` : '';
      return `${title}${chunk.content}${footer}`;
    })
    .join('\n\n---\n\n');
  return {
    id: Date.now(),
    role: 'user',
    content: prompt,
    conversation_id: message.conversation_id,
    actor: 'system',
    status: 'completed',
    task_id: message.task_id,
    agent_id: message.agent_id,
    run_id: message.run_id,
  };
}

export async function getValidRecentMessages(message: Message, limit: number): Promise<Message[]> {
  const filter = message.task_id
    ? {
        runId: message.run_id,
        taskId: message.task_id,
      }
    : undefined;
  const messages = await db.getRecentMessages(message.conversation_id, limit, filter);

  const result: Message[] = [];
  let hasSeenNonToolMessage = false;

  for (const message of messages) {
    // skip system messages
    if (message.role === 'system') {
      continue;
    }

    // skip error user message since it's not useful for context
    if (message.role === 'user' && message.status === 'error') {
      continue;
    }

    // Skip leading tool messages (tool messages before any user/assistant messages)
    if (message.role === 'tool' && !hasSeenNonToolMessage) {
      continue;
    }

    // Mark that we've seen a non-tool message
    if (message.role !== 'tool') {
      hasSeenNonToolMessage = true;
    }

    if (message.role === 'tool') {
      result.push({
        ...message,
        content: message.status === 'completed' ? 'Tool call success' : 'Tool call failed',
        tool_calls: undefined,
      });
    } else {
      result.push(message);
    }
  }

  // Remove trailing user messages from the end
  while (result.length > 0 && result[result.length - 1].role === 'user') {
    result.pop();
  }

  return result;
}
