import { WorkflowNodeType } from '../types';

/**
 * example of send<PERSON>eys node definition
{
  id: 'press-enter',
  description: 'Press Enter key',
  type: 'library',
  libraryId: 'lib:sendKeys',
  timeout: 10000,
  params: {
    keys: 'Enter',
    tabId: '{{tabId}}',
  },
}
 */

export const SENDKEYS_NODES: WorkflowNodeType[] = [
  {
    id: 'get-key-info',
    type: 'script',
    target: { tabId: '{{params.tabId}}' },
    code: `
      const keyMap = {
        'Escape': { key: 'Escape', code: 'Escape', keyCode: 27 },
        'Enter': { key: 'Enter', code: 'Enter', keyCode: 13 },
        'Tab': { key: 'Tab', code: 'Tab', keyCode: 9 },
        'Backspace': { key: 'Backspace', code: 'Backspace', keyCode: 8 },
        'Delete': { key: 'Delete', code: 'Delete', keyCode: 46 },
        'Home': { key: 'Home', code: 'Home', keyCode: 36 },
        'End': { key: 'End', code: 'End', keyCode: 35 },
        'PageUp': { key: 'PageUp', code: 'PageUp', keyCode: 33 },
        'PageDown': { key: 'PageDown', code: 'PageDown', keyCode: 34 },
        'ArrowLeft': { key: 'ArrowLeft', code: 'ArrowLeft', keyCode: 37 },
        'ArrowUp': { key: 'ArrowUp', code: 'ArrowUp', keyCode: 38 },
        'ArrowRight': { key: 'ArrowRight', code: 'ArrowRight', keyCode: 39 },
        'ArrowDown': { key: 'ArrowDown', code: 'ArrowDown', keyCode: 40 },
        'Space': { key: ' ', code: 'Space', keyCode: 32 },
        'Control': { key: 'Control', code: 'ControlLeft', keyCode: 17 },
        'Ctrl': { key: 'Control', code: 'ControlLeft', keyCode: 17 },
        'Shift': { key: 'Shift', code: 'ShiftLeft', keyCode: 16 },
        'Alt': { key: 'Alt', code: 'AltLeft', keyCode: 18 },
        'Meta': { key: 'Meta', code: 'MetaLeft', keyCode: 91 },
        'F1': { key: 'F1', code: 'F1', keyCode: 112 },
        'F2': { key: 'F2', code: 'F2', keyCode: 113 },
        'F3': { key: 'F3', code: 'F3', keyCode: 114 },
        'F4': { key: 'F4', code: 'F4', keyCode: 115 },
        'F5': { key: 'F5', code: 'F5', keyCode: 116 },
        'F6': { key: 'F6', code: 'F6', keyCode: 117 },
        'F7': { key: 'F7', code: 'F7', keyCode: 118 },
        'F8': { key: 'F8', code: 'F8', keyCode: 119 },
        'F9': { key: 'F9', code: 'F9', keyCode: 120 },
        'F10': { key: 'F10', code: 'F10', keyCode: 121 },
        'F11': { key: 'F11', code: 'F11', keyCode: 122 },
        'F12': { key: 'F12', code: 'F12', keyCode: 123 }
      };
      
      const key = $params.keys;
      const keyInfo = keyMap[key] || { key: key, code: key, keyCode: key.charCodeAt(0) };
      return keyInfo;
    `,
  },
  {
    id: 'send-key-down',
    type: 'debugger',
    command: 'Input.dispatchKeyEvent',
    action: 'command',
    params: {
      type: 'keyDown',
      key: '{{get-key-info.key}}',
      code: '{{get-key-info.code}}',
      windowsVirtualKeyCode: '{{get-key-info.keyCode}}',
    },
    target: { tabId: '{{params.tabId}}' },
  },
  {
    id: 'send-key-up',
    type: 'debugger',
    command: 'Input.dispatchKeyEvent',
    action: 'command',
    params: {
      type: 'keyUp',
      key: '{{get-key-info.key}}',
      code: '{{get-key-info.code}}',
      windowsVirtualKeyCode: '{{get-key-info.keyCode}}',
    },
    target: { tabId: '{{params.tabId}}' },
  },
];
