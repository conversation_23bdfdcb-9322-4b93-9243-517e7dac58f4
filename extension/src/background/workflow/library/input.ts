import { WorkflowNodeType } from '../types';

/**
 * example of input node definition
{
  id: 'input-username',
  description: 'Input username into the form',
  type: 'library',
  libraryId: 'lib:input',
  timeout: 10000,
  params: {
    selector: 'input[name="username"]',
    value: 'testuser',
    tabId: '{{tabId}}',
  },
}
 */
export const INPUT_NODES: WorkflowNodeType[] = [
  {
    type: 'script',
    target: { tabId: '{{params.tabId}}' },
    code: `
            let el = null;
            while (!el) {
              el = document.querySelector($params.selector);
              if (!el) {
                await new Promise(resolve => setTimeout(resolve, 100));
              }
            }
            const rect = el.getBoundingClientRect();
            return {x: rect.left + rect.width/2, y: rect.top + rect.height/2};
          `,
    id: 'get-input-coords',
  },
  {
    id: 'click-input',
    type: 'debugger',
    command: 'Input.dispatchMouseEvent',
    action: 'command',
    params: {
      y: '{{get-input-coords.y}}',
      x: '{{get-input-coords.x}}',
      type: 'mousePressed',
      button: 'left',
      clickCount: 1,
    },
    target: { tabId: '{{params.tabId}}' },
  },
  {
    id: 'release-input-click',
    type: 'debugger',
    command: 'Input.dispatchMouseEvent',
    action: 'command',
    params: {
      y: '{{get-input-coords.y}}',
      x: '{{get-input-coords.x}}',
      type: 'mouseReleased',
      button: 'left',
      clickCount: 1,
    },
    target: { tabId: '{{params.tabId}}' },
  },
  {
    id: 'type-text',
    type: 'debugger',
    command: 'Input.insertText',
    action: 'command',
    params: {
      text: '{{params.value}}',
    },
    target: { tabId: '{{params.tabId}}' },
  },
];
