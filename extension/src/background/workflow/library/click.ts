import { WorkflowNodeType } from '../types';

// example of click node definition
// {
//   id: 'click-post-button',
//   description: 'Click the post button',
//   type: 'library',
//   library: 'lib:click',
//   timeout: 10000,
//   params: {
//     selector: 'button.post',
//     tabId: '{{tabId}}',
//   },
// }

export const CLICK_NODES: WorkflowNodeType[] = [
  {
    type: 'script',
    target: { tabId: '{{params.tabId}}' },
    code: `
            let el = null;
            while (!el) {
              el = document.querySelector($params.selector);
              if (!el) {
                await new Promise(resolve => setTimeout(resolve, 100));
              }
            }
            const rect = el.getBoundingClientRect();
            return {x: rect.left + rect.width/2, y: rect.top + rect.height/2};
          `,
    id: 'get-element-coords',
  },
  {
    id: 'click-element',
    type: 'debugger',
    command: 'Input.dispatchMouseEvent',
    action: 'command',
    params: {
      y: '{{get-element-coords.y}}',
      x: '{{get-element-coords.x}}',
      type: 'mousePressed',
      button: 'left',
      clickCount: 1,
    },
    target: { tabId: '{{params.tabId}}' },
  },
  {
    id: 'click-element-release',
    type: 'debugger',
    command: 'Input.dispatchMouseEvent',
    action: 'command',
    params: {
      y: '{{get-element-coords.y}}',
      x: '{{get-element-coords.x}}',
      type: 'mouseReleased',
      button: 'left',
      clickCount: 1,
    },
    target: { tabId: '{{params.tabId}}' },
  },
];
