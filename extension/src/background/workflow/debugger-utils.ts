/**
 * Stateless helpers for Chrome Debugger attach/detach operations.
 */

export async function isDebuggerAttached(tabId: number): Promise<boolean> {
  return new Promise(resolve => {
    try {
      chrome.debugger.getTargets((targets: chrome.debugger.TargetInfo[]) => {
        try {
          const attached = Array.isArray(targets)
            ? targets.some(t => t && t.tabId === tabId && t.attached === true)
            : false;
          resolve(attached);
        } catch {
          resolve(false);
        }
      });
    } catch {
      resolve(false);
    }
  });
}

export async function attachDebuggerIfNeeded(
  tabId: number,
  protocolVersion = '1.0'
): Promise<void> {
  const attached = await isDebuggerAttached(tabId);
  if (attached) return;
  await chrome.debugger.attach({ tabId }, protocolVersion);
}

export async function detachDebuggerIfAttached(tabId: number): Promise<void> {
  const attached = await isDebuggerAttached(tabId);
  if (!attached) return;
  try {
    await chrome.debugger.detach({ tabId });
  } catch {
    // ignore
  }
}
