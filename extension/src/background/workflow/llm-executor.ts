import OpenAI from 'openai';
import { ResolvedLLMNode, WorkflowExecutionContext, ExecutionResult } from './types';
import { db } from '~/storages/indexdb';

const DEFAULT_MODEL = 'google/gemini-2.5-flash';

/**
 * LLM Node executor for AI conversation and content generation
 */
export class LLMNodeExecutor {
  private openaiClient: OpenAI | null = null;

  /**
   * Initialize OpenAI client
   */
  private async getOpenAIClient(): Promise<OpenAI> {
    if (!this.openaiClient) {
      const model = await db.getSelectModel();
      this.openaiClient = new OpenAI({
        baseURL: model.apiUrl,
        apiKey: model.apiKey,
        dangerouslyAllowBrowser: true,
      });
    }
    return this.openaiClient;
  }

  /**
   * Execute LLM node
   */
  async execute(
    node: ResolvedLLMNode,
    _context: WorkflowExecutionContext
  ): Promise<ExecutionResult> {
    try {
      const openai = await this.getOpenAIClient();
      const messages = this.prepareMessages(node);
      const response = await openai.chat.completions.create({
        model: node.model || DEFAULT_MODEL,
        messages,
      });
      const result = this.extractResponse(response);
      return {
        success: true,
        result: JSON.stringify(result),
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      console.error(`[LLMNode] Execution failed: ${message}`);
      return {
        success: false,
        error: `LLM execution failed: ${message}`,
      };
    }
  }

  /**
   * Prepare messages for LLM
   */
  private prepareMessages(
    node: ResolvedLLMNode
  ): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
    const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [];
    if (node.systemPrompt) {
      messages.push({
        role: 'system',
        content: node.systemPrompt,
      });
    }
    messages.push({
      role: 'user',
      content: node.prompt,
    });
    return messages;
  }

  /**
   * Extract and format response from LLM
   */
  private extractResponse(response: OpenAI.Chat.Completions.ChatCompletion): string | object {
    const content = response.choices[0]?.message?.content;
    try {
      return JSON.parse(content || '{}');
    } catch (error) {
      console.error(`[LLMNode] Failed to parse response: ${error}`);
      return { text: content };
    }
  }
}

// Singleton instance
export const llmExecutor = new LLMNodeExecutor();
