import { db } from '~/storages/indexdb';

/**
 * User Script Manager for Workflow Runtime
 * Handles registration and execution of user scripts to bypass CSP restrictions
 */

// Type-safe access to userScripts API with null check
const getChromeUserScripts = () => {
  const userScripts = (chrome as any).userScripts;
  if (!userScripts) {
    throw new Error(
      'chrome.userScripts API is not available. Ensure "userScripts" permission is granted.'
    );
  }
  return userScripts as {
    register(
      scripts: Array<{
        id: string;
        matches: string[];
        runAt?: 'document_start' | 'document_end' | 'document_idle';
        allFrames?: boolean;
        js: { code: string }[];
      }>
    ): Promise<void>;
    unregister(filter: { ids: string[] }): Promise<void>;
  };
};

export class UserScriptManager {
  private registeredScripts = new Set<string>();

  /**
   * Register all installed miniapp user scripts from IndexedDB.
   */
  async registerUserScripts(): Promise<void> {
    try {
      const installations = await db.getAllInstallations();
      for (const installation of installations) {
        await this.registerOneUserScript(installation);
      }
    } catch (error) {
      console.warn('Failed to register user scripts:', error);
    }
  }

  /**
   * Register one user script for a given miniapp installation.
   * Expects: { appId: number, code: string, deployed_at: number }
   */
  async registerOneUserScript(installation: {
    appId: number;
    code: string;
    deployed_at: number;
  }): Promise<void> {
    const scriptId = `miniapp_${installation.appId}`;
    if (this.registeredScripts.has(scriptId)) return;

    try {
      const chromeUserScripts = getChromeUserScripts();
      const wrappedCode = this.wrapUserCode(installation.code);
      const matches = ['<all_urls>']; // TODO: make it configurable

      await chromeUserScripts.unregister({ ids: [scriptId] }).catch(() => {});
      await chromeUserScripts.register([
        {
          id: scriptId,
          matches,
          runAt: 'document_idle',
          allFrames: false,
          js: [{ code: wrappedCode }],
        },
      ]);

      this.registeredScripts.add(scriptId);
    } catch (error) {
      console.warn(`Failed to register user script for miniapp ${scriptId}:`, error);
    }
  }

  /**
   * Wrap user code with status tracking and error handling
   */
  private wrapUserCode(userCode: string): string {
    return `
      (async function() {
        try {
          await (async function() { ${userCode} })();
        } catch (error) {
          console.error('[user-script] execution error:', error);
        }
      })();
    `;
  }

  /**
   * Cleanup registered user script
   */
  private async cleanup(scriptId: string): Promise<void> {
    try {
      if (this.registeredScripts.has(scriptId)) {
        const chromeUserScripts = getChromeUserScripts();
        await chromeUserScripts.unregister({ ids: [scriptId] });
        this.registeredScripts.delete(scriptId);
      }
    } catch (error) {
      // Ignore cleanup errors
      console.warn(`Failed to cleanup user script ${scriptId}:`, error);
    }
  }

  /**
   * Cleanup all registered scripts
   */
  async cleanupAll(): Promise<void> {
    const scriptIds = Array.from(this.registeredScripts);
    for (const scriptId of scriptIds) {
      await this.cleanup(scriptId);
    }
  }
}

// Singleton instance
export const userScriptManager = new UserScriptManager();
