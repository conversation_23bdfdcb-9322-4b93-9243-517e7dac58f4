/**
 * Workflow Node Type Definitions
 *
 * This file defines all the node types for the config-based workflow system.
 * Each node represents a step in a workflow that can be executed across multiple pages.
 */

// Runtime input types - allow referencing outputs from previous nodes
export type RuntimeInput<T> = T | string; // Either static value or variable reference like "{{nodeId.result}}"

// Base node interface - common properties for all node types
interface BaseNode {
  id: string; // Unique identifier and human-readable name
  description?: string;
  timeout?: number; // ms, default 30000
}

// 1. SCRIPT - Execute JavaScript in page context
export interface ScriptNode extends BaseNode {
  type: 'script';
  target: {
    tabId?: RuntimeInput<number>;
  };
  code: RuntimeInput<string>; // JavaScript to execute
}

// 2. NAVIGATION - Navigate to URL or manage tabs
export interface NavigationNode extends BaseNode {
  type: 'navigation';
  action: 'open' | 'close' | 'switch' | 'reload' | 'back' | 'forward';
  target?: {
    url?: RuntimeInput<string>; // Required for 'open'
    tabId?: RuntimeInput<number>; // Required for 'close', 'switch', optional for others
  };
  loadTimeout?: number; // Timeout for waiting (default: 30000ms)
}

// 3. WAIT - Simple time delay
export interface WaitNode extends BaseNode {
  type: 'wait';
  duration: RuntimeInput<number>; // Milliseconds to wait
}

// 4. DEBUGGER - Chrome DevTools Protocol commands
export interface DebuggerNode extends BaseNode {
  type: 'debugger';
  action: 'attach' | 'detach' | 'command';
  target: {
    tabId?: RuntimeInput<number>;
  };
  command?: RuntimeInput<string>; // CDP command like 'Input.dispatchMouseEvent'
  params?: Record<string, RuntimeInput<any>>; // CDP command parameters
}

export interface LibraryNode extends BaseNode {
  type: 'library';
  libraryId: string;
  params?: Record<string, RuntimeInput<any>>; // Parameters for the library workflow
}

// 6. LLM - AI conversation and content generation
export interface LLMNode extends BaseNode {
  type: 'llm';
  prompt: RuntimeInput<string>; // The prompt to send to LLM
  model?: RuntimeInput<string>; // Model name, defaults to system default
  systemPrompt?: RuntimeInput<string>; // Custom system prompt
  // we always assume the result is a JSON object
  jsonSchema?: RuntimeInput<string>; // JSON schema for the output
}

// Union type for all node types
export type WorkflowNodeType =
  | ScriptNode
  | NavigationNode
  | WaitNode
  | DebuggerNode
  | LLMNode
  | LibraryNode;

// Resolved node types (after runtime input resolution)
export interface ResolvedScriptNode extends Omit<ScriptNode, 'target' | 'code'> {
  target: {
    tabId?: number;
  };
  code: string;
}

export interface ResolvedNavigationNode extends Omit<NavigationNode, 'target'> {
  target?: {
    url?: string;
    tabId?: number;
  };
  loadTimeout?: number;
}

export interface ResolvedWaitNode extends Omit<WaitNode, 'duration'> {
  duration: number;
}

export interface ResolvedDebuggerNode extends Omit<DebuggerNode, 'target' | 'command' | 'params'> {
  target: {
    tabId?: number;
  };
  command?: string;
  params?: Record<string, any>;
}

export interface ResolvedLibraryNode extends Omit<LibraryNode, 'libraryId' | 'params'> {
  libraryId: string;
  params?: Record<string, any>;
}

export interface ResolvedLLMNode
  extends Omit<LLMNode, 'prompt' | 'model' | 'systemPrompt' | 'jsonSchema'> {
  prompt: string;
  model?: string;
  systemPrompt?: string;
  jsonSchema?: string;
}

// Execution context and results
export interface WorkflowExecutionContext {
  workflowId: string;
  variables: Record<string, string>; // Global variables as JSON strings
  currentNodeId?: string;
  startTime: number;
  tabContexts: Map<number, any>; // Tab-specific data
  settings: {
    defaultTimeout: number;
    maxRetries: number;
    debugMode: boolean;
  };
}

export interface ExecutionResult {
  success: boolean;
  result?: unknown;
  error?: string;
  executionTime?: number;
  nodeResults?: Record<string, unknown>; // Results from each node
}

// Runtime input resolution utilities
export interface RuntimeResolver {
  /**
   * Resolve a runtime input value using current workflow variables
   * @param input Static value or variable reference like "{{nodeId}}"
   * @param variables Current workflow variables (JSON strings)
   * @returns Resolved value
   */
  resolve<T>(input: RuntimeInput<T>, variables: Record<string, string>): T;

  /**
   * Resolve all runtime inputs in an object
   * @param obj Object containing runtime inputs
   * @param variables Current workflow variables (JSON strings)
   * @returns Object with all inputs resolved
   */
  resolveObject<T>(obj: T, variables: Record<string, string>): T;
}
