import { db } from '~/storages/indexdb';
import {
  APIClient,
  Message,
  ChatOptions,
  Agent,
  ChatStatus,
  MemoryMetadata,
  MessageContent,
} from '@the-agent/shared';
import { createApiClient } from '~/services/api/client';
import { createBrowserAgent } from '~/agents/browser';
import OpenAI from 'openai';
import { storeMemory, filterToolMessage } from '~/utils/memory';
import { getMessage } from '~/utils/i18n';
import { handleApiError } from '~/utils/conversation';
import { backupConversation } from '~/services/conversation';
import { createWorkflowAgent } from '~/agents/workflow';
import { MiniAppLocal as MiniApp } from '~/types/miniapp';

export interface ChatHandlerOptions {
  currentConversationId: number;
  conversationType: 'default' | 'remote' | 'miniapp';
  setStatus: (status: ChatStatus) => void;
  onMessageUpdate?: (message: Message) => void;
  onMessageComplete?: (message: Message) => void;
}

export class ChatHandler {
  private apiClient: APIClient | null = null;
  private options: ChatHandlerOptions;
  private agent: Agent | null = null;

  constructor(options: ChatHandlerOptions) {
    this.options = options;
    // Ensure 'this' context is preserved when methods are passed as callbacks
    this.handleSubmit = this.handleSubmit.bind(this);
  }

  async handleSubmit(content: MessageContent) {
    if (!content) {
      return;
    }

    this.options.setStatus?.('running');
    if (!this.apiClient) {
      this.apiClient = await createApiClient();
    }

    try {
      const model = await db.getSelectModel();
      const llmClient = new OpenAI({
        baseURL: model.apiUrl,
        apiKey: model.apiKey,
        dangerouslyAllowBrowser: true,
      });
      const chatOptions = this.buildChatOptions(this.apiClient);

      if (this.agent !== null) {
        console.error('Agent is still running');
        throw new Error('Agent is still running');
      }

      const userMessage: Message = {
        id: Date.now(),
        conversation_id: chatOptions.conversationId,
        role: 'user',
        content: JSON.stringify(content),
        status: 'pending',
        actor: 'user',
      };
      chatOptions.onMessageUpdate?.(userMessage);
      if (this.options.conversationType === 'miniapp') {
        this.agent = createWorkflowAgent(model.name, llmClient, this.apiClient);
      } else {
        this.agent = createBrowserAgent(model.name, llmClient, this.apiClient);
      }
      await this.agent.run(userMessage, { chatOptions });
    } catch (error: unknown) {
      handleApiError(error, this.options.currentConversationId, getMessage);
    } finally {
      this.abort();
      this.agent = null;
      this.options.setStatus?.('idle');
    }
  }

  private buildChatOptions(apiClient: APIClient): ChatOptions {
    return {
      conversationId: this.options.currentConversationId,
      onMessageUpdate: async (message: Message) => {
        await db.saveMessage(message);
        this.options.onMessageUpdate?.(message);
      },
      onMessageComplete: async (message: Message) => {
        if (message.status !== 'error') {
          message.status = 'completed';
        }
        await db.saveMessage(message);
        this.options.onMessageComplete?.(message);
        backupMessage(apiClient, message);
      },
      onStatusChange: async (status: ChatStatus) => {
        this.options.setStatus(status);
      },
      onChatComplete: async (messages: Message[]) => {
        if (this.apiClient && messages.length > 0) {
          const metadata = buildMemoryMetadata(messages[0]);
          await storeMemory(this.apiClient, messages, metadata);
        }
      },
    };
  }

  abort() {
    if (this.agent) {
      this.agent.abort();
      this.agent = null;
    }
  }
}

function buildMemoryMetadata(message: Message): MemoryMetadata {
  const metadata: MemoryMetadata = {
    conversationId: message.conversation_id.toString(),
  };

  if (message.task_id) {
    metadata.taskId = message.task_id;
  }
  if (message.agent_id) {
    metadata.agentId = message.agent_id;
  }
  if (message.run_id) {
    metadata.runId = message.run_id;
  }
  return metadata;
}

async function backupMessage(apiClient: APIClient, message: Message) {
  try {
    const conversation = await db.getConversation(message.conversation_id);
    await backupConversation(apiClient, conversation!);
    if (conversation?.type === 'miniapp') {
      const miniapp = await db.getMiniappByConversationId(conversation.id);
      await backupMiniApp(apiClient, miniapp!);
    }

    // Filter large data for remote storage
    const filteredMessage = filterToolMessage(message);
    await apiClient.saveMessageV2({ message: filteredMessage || message });
  } catch (error) {
    console.log('failed to save message to server', JSON.stringify(error));
    message.status = 'warning';
    message.error = 'failed to sync with backend';
    await db.saveMessage(message);
  }
}

async function backupMiniApp(apiClient: APIClient, miniapp: MiniApp) {
  if (miniapp.sync_status !== 'local') {
    return;
  }

  try {
    await db.updateMiniapp(miniapp.id, { sync_status: 'syncing' });
    const conversation = await db.getConversation(miniapp.conversation_id);
    await backupConversation(apiClient, conversation!);
    await apiClient.saveMiniApp({ miniapp });
    await db.updateMiniapp(miniapp.id, { sync_status: 'remote' });
  } catch (error) {
    console.log('failed to save message to server', JSON.stringify(error));
    await db.updateMiniapp(miniapp.id, { sync_status: 'local' });
  }
}
