# LLM Node Usage Examples

LLMNode is the 6th node type in the workflow runtime, used to integrate AI capabilities into automation workflows.

## Basic Usage

### 1. Simple Text Generation

```json
{
  "id": "generate-text",
  "type": "llm",
  "prompt": "Write a poem about AI",
  "context": {
    "temperature": 0.8,
    "maxTokens": 200
  },
  "outputFormat": "text"
}
```

### 2. Content Generation Based on Page Content

```json
{
  "id": "analyze-page",
  "type": "llm",
  "prompt": "Analyze the main content of this page",
  "context": {
    "includePageContent": true,
    "temperature": 0.3
  },
  "outputFormat": "text"
}
```

### 3. Structured Output

```json
{
  "id": "extract-info",
  "type": "llm",
  "prompt": "Extract key information from the page content",
  "context": {
    "includePageContent": true,
    "systemPrompt": "You are an information extraction assistant, please return results in JSON format"
  },
  "outputFormat": "json"
}
```

## Real-World Application Scenarios

### Scenario 1: Auto Tweet Generation

```json
[
  {
    "id": "get-page-content",
    "type": "script",
    "code": "return document.body.innerText.substring(0, 1000);"
  },
  {
    "id": "generate-tweet",
    "type": "llm",
    "prompt": "Based on the page content, generate an engaging tweet under 280 characters",
    "context": {
      "includePageContent": true,
      "temperature": 0.7
    },
    "outputFormat": "text"
  },
  {
    "id": "type-tweet",
    "type": "debugger",
    "action": "command",
    "command": "Input.insertText",
    "params": { "text": "{{generate-tweet}}" }
  }
]
```

### Scenario 2: Intelligent Decision Making

```json
[
  {
    "id": "analyze-page",
    "type": "llm",
    "prompt": "Analyze the page and decide what action to take next",
    "context": {
      "includePageContent": true,
      "systemPrompt": "You are a web automation assistant, return JSON format: {\"action\": \"click|type|navigate\", \"target\": \"selector\", \"reason\": \"reason\"}"
    },
    "outputFormat": "json"
  },
  {
    "id": "execute-decision",
    "type": "workflow",
    "nodes": [
      {
        "id": "click-target",
        "type": "debugger",
        "action": "command",
        "command": "Input.dispatchMouseEvent",
        "params": {
          "type": "mousePressed",
          "x": "{{analyze-page.coordinates.x}}",
          "y": "{{analyze-page.coordinates.y}}",
          "button": "left"
        }
      }
    ]
  }
]
```

### Scenario 3: Content Optimization

```json
[
  {
    "id": "get-user-input",
    "type": "script",
    "code": "return document.querySelector('#user-input').value;"
  },
  {
    "id": "optimize-content",
    "type": "llm",
    "prompt": "Optimize this text to make it more suitable for social media posting",
    "context": {
      "temperature": 0.5,
      "maxTokens": 500
    },
    "outputFormat": "text"
  }
]
```

## Parameter Reference

### Basic Parameters

- `id`: Unique node identifier
- `type`: Fixed as "llm"
- `prompt`: Text prompt to send to AI

### Context Parameters

- `includePageContent`: Whether to include current page content
- `includeMemory`: Whether to include relevant memories
- `systemPrompt`: Custom system prompt
- `temperature`: Creativity level (0-2, default 0.7)
- `maxTokens`: Maximum tokens to generate (default 1000)

### Output Formats

- `text`: Plain text output
- `json`: JSON format output
- `structured`: Structured output with metadata

## Best Practices

1. **Clear Prompts**: Use clear and specific prompt words
2. **Appropriate Temperature**: Use high temperature (0.8-1.0) for creative tasks, low temperature (0.1-0.3) for analysis tasks
3. **Error Handling**: Add error handling nodes in workflows
4. **Test Validation**: Test with simple examples first, then build complex workflows

## Important Notes

1. LLM calls require network connection and API keys
2. Response time depends on model and network conditions
3. Output quality depends on prompt quality
4. Recommend setting reasonable maxTokens limits
