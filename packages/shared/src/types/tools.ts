type Property = {
  type: string;
  items?: Property;
  description?: string;
  enum?: string[];
  properties?: PropertyMap;
};

type PropertyMap = Record<string, Property>;

export interface ToolScope {
  urlPatterns: string[];
  agents: string[];
}

export interface ToolDescription {
  name: string;
  description: string;
  parameters?: {
    type: string;
    properties: PropertyMap;
    required?: string[];
  };
  returns?: {
    type: string;
    description: string;
    properties?: PropertyMap;
  };
  scope: ToolScope;
}

export interface WebContextInput {
  tabId: number;
  pageId: number;
}

export interface WebContext extends WebContextInput {
  title: string;
  url: string;
  sourceTabId?: number;
  createdAt: number;
}
