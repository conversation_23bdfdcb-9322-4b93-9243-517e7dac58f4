import { z } from 'zod';

// Common types
export const MessageRoleSchema = z.enum(['system', 'user', 'assistant', 'tool']);
export type MessageRole = z.infer<typeof MessageRoleSchema>;

export const MessageStatusSchema = z.enum(['pending', 'completed', 'error', 'warning']);
export type MessageStatus = z.infer<typeof MessageStatusSchema>;

export const ToolCallResultSchema = z.object({
  success: z.boolean(),
  data: z.unknown().nullable().optional(),
  error: z.string().nullable().optional(),
});
export type ToolCallResult = z.infer<typeof ToolCallResultSchema>;

export const ToolCallSchema = z.object({
  id: z.string(),
  type: z.string(),
  function: z.object({
    name: z.string(),
    arguments: z.string(),
  }),
  result: ToolCallResultSchema.nullable().optional(),
});
export type ToolCall = z.infer<typeof ToolCallSchema>;

export const TokenUsageSchema = z.object({
  prompt_tokens: z.number(),
  completion_tokens: z.number(),
  total_tokens: z.number(),
});
export type TokenUsage = z.infer<typeof TokenUsageSchema>;

export const ChatMessageSchema = z.object({
  role: MessageRoleSchema,
  content: z.string().nullable().optional(),
  reasoning: z.string().nullable().optional(),
  name: z.string().nullable().optional(),
  tool_calls: z.array(ToolCallSchema).nullable().optional(),
  tool_call_id: z.string().nullable().optional(),
  token_usage: TokenUsageSchema.nullable().optional(),
});

export type ChatMessage = z.infer<typeof ChatMessageSchema>;

export const MessageContentPartSchema = z.union([
  z.object({
    type: z.literal('text'),
    text: z.string(),
  }),
  z.object({
    type: z.literal('image_url'),
    image_url: z.object({ url: z.string() }),
  }),
]);
export type MessageContentPart = z.infer<typeof MessageContentPartSchema>;

export const MessageContentSchema = z.union([z.string(), z.array(MessageContentPartSchema)]);
export type MessageContent = z.infer<typeof MessageContentSchema>;

export const MessageSchema = ChatMessageSchema.extend({
  id: z.number(),
  conversation_id: z.number(),
  run_id: z.string().nullable().optional(),
  agent_id: z.string().nullable().optional(),
  actor: z.enum(['system', 'user']).nullable().optional(),
  task_id: z.string().nullable().optional(),
  status: MessageStatusSchema.nullable().optional(),
  error: z.string().nullable().optional(),
  metadata: z.record(z.unknown()).nullable().optional(),
  // deprecated
  display_text: z.string().nullable().optional(),
  tool_call_arguments: z.string().nullable().optional(),
  tool_call_result: z.any().nullable().optional(),
});
export type Message = z.infer<typeof MessageSchema>;

export const ConversationTypeSchema = z.enum(['default', 'remote', 'miniapp']);
export type ConversationType = z.infer<typeof ConversationTypeSchema>;

export const ConversationSchema = z.object({
  id: z.number(),
  title: z.string().nullable().optional(),
  type: ConversationTypeSchema.nullable().optional(),
  last_selected_at: z.number().nullable().optional(),
  messages: z.array(MessageSchema).nullable().optional(),
});
export type Conversation = z.infer<typeof ConversationSchema>;

// conversation handlers
export const CreateConversationRequestSchema = z.object({
  id: z.number(),
  type: ConversationTypeSchema.nullable().optional(),
  last_selected_at: z.number().nullable().optional(),
});
export type CreateConversationRequest = z.infer<typeof CreateConversationRequestSchema>;

export const CreateConversationResponseSchema = z.object({
  id: z.number(),
});
export type CreateConversationResponse = z.infer<typeof CreateConversationResponseSchema>;

export const DeleteConversationRequestSchema = z.object({
  id: z.number(),
});
export type DeleteConversationRequest = z.infer<typeof DeleteConversationRequestSchema>;

export const DeleteConversationResponseSchema = z.object({
  deleted: z.boolean(),
});
export type DeleteConversationResponse = z.infer<typeof DeleteConversationResponseSchema>;

export const ListConversationsRequestSchema = z.object({
  startFrom: z.number().default(0),
});
export type ListConversationsRequest = z.infer<typeof ListConversationsRequestSchema>;

export const ListConversationsResponseSchema = z.object({
  conversations: z.array(ConversationSchema),
});
export type ListConversationsResponse = z.infer<typeof ListConversationsResponseSchema>;

export const UpdateConversationRequestSchema = z.object({
  id: z.number(),
  last_selected_at: z.number().optional(),
  title: z.string().optional(),
  type: ConversationTypeSchema.optional(),
  status: z.string().optional(),
});
export type UpdateConversationRequest = z.infer<typeof UpdateConversationRequestSchema>;

export const UpdateConversationResponseSchema = z.object({
  success: z.boolean(),
});
export type UpdateConversationResponse = z.infer<typeof UpdateConversationResponseSchema>;

// message handlers
export const SaveMessageRequestSchemaV2 = z.object({
  message: MessageSchema,
});
export type SaveMessageRequestV2 = z.infer<typeof SaveMessageRequestSchemaV2>;

export const QAItemSchema = z.object({
  question: z.string(),
  answer: z.string(),
});
export type QAItem = z.infer<typeof QAItemSchema>;

export const SaveMessageResponseSchemaV2 = z.object({
  success: z.boolean(),
});
export type SaveMessageResponseV2 = z.infer<typeof SaveMessageResponseSchemaV2>;

// Chat completion parameters
export const ChatCompletionCreateParamSchema = z.object({
  messages: z.array(ChatMessageSchema),
  model: z.string(),
  frequency_penalty: z.number().nullable().optional(),
  logit_bias: z.record(z.number()).nullable().optional(),
  max_tokens: z.number().nullable().optional(),
  n: z.number().nullable().optional(),
  presence_penalty: z.number().nullable().optional(),
  response_format: z.record(z.string()).nullable().optional(),
  seed: z.number().nullable().optional(),
  stop: z
    .union([z.string(), z.array(z.string())])
    .nullable()
    .optional(),
  stream: z.boolean().default(false),
  temperature: z.number().nullable().optional(),
  top_p: z.number().nullable().optional(),
  tools: z.array(z.record(z.unknown())).nullable().optional(),
  tool_choice: z
    .union([z.string(), z.record(z.unknown())])
    .nullable()
    .optional(),
  user: z.string().nullable().optional(),
});

export type ChatCompletionCreateParam = z.infer<typeof ChatCompletionCreateParamSchema>;

export const ChatCompletionResponseSchema = z.object({
  id: z.string(),
  object: z.string(),
  created: z.number(),
  model: z.string(),
  choices: z.array(
    z.object({
      index: z.number(),
      message: z.object({
        role: MessageRoleSchema,
        content: z.string(),
      }),
      finish_reason: z.string().nullable(),
    })
  ),
  usage: z.object({
    prompt_tokens: z.number(),
    completion_tokens: z.number(),
    total_tokens: z.number(),
  }),
});
export type ChatCompletionResponse = z.infer<typeof ChatCompletionResponseSchema>;

// payment handlers
export const OrderStatusSchema = z.enum([
  'pending',
  'completed',
  'cancelled',
  'failed',
  'finalized',
]);
export type OrderStatus = z.infer<typeof OrderStatusSchema>;

export const TransactionTypeSchema = z.enum(['credit', 'debit']);
export type TransactionType = z.infer<typeof TransactionTypeSchema>;

export const TransactionReasonSchema = z.enum([
  'new_user',
  'order_pay',
  'system_add',
  'completion',
  'coupon_code',
]);
export type TransactionReason = z.infer<typeof TransactionReasonSchema>;

export const CreditLogSchema = z.object({
  id: z.number(),
  tx_credits: z.number(),
  tx_type: z.string(),
  tx_reason: z.string().nullable().optional(),
  model: z.string().nullable().optional(),
  created_at: z.string(),
});
export type CreditLog = z.infer<typeof CreditLogSchema>;

export const GetUserResponseSchema = z.object({
  id: z.string(),
  email: z.string().nullable().optional(),
  api_key: z.string(),
  api_key_enabled: z.boolean(),
  balance: z.number(),
  permission: z.object({
    can_save_site_message: z.boolean(),
  }),
});
export type GetUserResponse = z.infer<typeof GetUserResponseSchema>;

// user handlers
export const GetUserBalanceResponseSchema = z.object({
  amount: z.number(),
});
export type GetUserBalanceDResponse = z.infer<typeof GetUserBalanceResponseSchema>;

export const RotateApiKeyResponseSchema = z.object({
  newApiKey: z.string(),
});
export type RotateApiKeyResponse = z.infer<typeof RotateApiKeyResponseSchema>;

export const GetCreditDailyRequestSchema = z.object({
  startDate: z.string().nullable().optional(),
  endDate: z.string().nullable().optional(),
});
export type GetCreditDailyRequest = z.infer<typeof GetCreditDailyRequestSchema>;

export const CreditDailyItemSchema = z.object({
  date: z.string(),
  credits: z.number(),
});
export type CreditDailyItem = z.infer<typeof CreditDailyItemSchema>;

export const GetCreditDailyResponseSchema = z.object({
  data: z.array(CreditDailyItemSchema),
});
export type GetCreditDailyResponse = z.infer<typeof GetCreditDailyResponseSchema>;

export const RedeemCouponRequestSchema = z.object({
  code: z.string(),
});
export type RedeemCouponRequest = z.infer<typeof RedeemCouponRequestSchema>;

export const RedeemCouponResponseSchema = z.object({
  added_credits: z.number(),
  total_credits: z.number(),
});
export type RedeemCouponResponse = z.infer<typeof RedeemCouponResponseSchema>;

// stripe handlers

export const StripeCheckoutRequestSchema = z.object({
  amount: z.number(),
});
export type StripeCheckoutRequest = z.infer<typeof StripeCheckoutRequestSchema>;

export const StripeCheckoutResponseSchema = z.object({
  order_id: z.number(),
  session_id: z.string(),
  public_key: z.string(),
});
export type StripeCheckoutResponse = z.infer<typeof StripeCheckoutResponseSchema>;

export const GenerateCouponCodeRequestSchema = z.object({
  campaign: z.string().optional(),
  credits: z.number().optional(),
  max_uses: z.number().optional(),
  expired_at: z.string().optional(),
});
export type GenerateCouponCodeRequest = z.infer<typeof GenerateCouponCodeRequestSchema>;

export const GenerateCouponCodeResponseSchema = z.object({
  coupon_code: z.string(),
});
export type GenerateCouponCodeResponse = z.infer<typeof GenerateCouponCodeResponseSchema>;

// Execution Step Types
export const ExecutionStepSchema = z.object({
  stepNumber: z.number(),
  agentAction: z.string(),
  actionResult: z.string(),
  keyFindings: z.string().optional(),
  navigationHistory: z.string().optional(),
  errors: z.string().optional(),
  currentContext: z.string(),
  timestamp: z.string(),
});
export type ExecutionStep = z.infer<typeof ExecutionStepSchema>;

export const TemplateRegisterRequestSchema = z.object({
  domain: z.string(),
  path: z.string(),
  tag_vector: z.array(z.number()).length(128),
  simhash: z.number(),
});
export type TemplateRegisterRequest = z.infer<typeof TemplateRegisterRequestSchema>;

export const TemplateSchema = z.object({
  id: z.number().int().positive(),
  domain: z.string(),
  path_pattern: z.string(),
});
export type Template = z.infer<typeof TemplateSchema>;

export const GetTemplatesRequestSchema = z.object({
  domain: z.string().nullable().optional(),
});
export type GetTemplatesRequest = z.infer<typeof GetTemplatesRequestSchema>;

export const TemplateRegisterResponseSchema = TemplateSchema.extend({
  matched: z.boolean(),
});
export type TemplateRegisterResponse = z.infer<typeof TemplateRegisterResponseSchema>;

export const GetTemplatesResponseSchema = z.object({
  templates: z.array(TemplateSchema),
});
export type GetTemplatesResponse = z.infer<typeof GetTemplatesResponseSchema>;

export const DevelopingSchema = z.object({
  code: z.string(),
  version: z.number(),
  updated_at: z.number(),
});
export type Developing = z.infer<typeof DevelopingSchema>;

export const InstallationSchema = z.object({
  code: z.string(),
  changelogs: z.string(),
  deployed_at: z.number(),
});
export type Installation = z.infer<typeof InstallationSchema>;

export const MiniAppStatusSchema = z.enum(['active', 'archived', 'deleted', 'testing']);
export type MiniAppStatus = z.infer<typeof MiniAppStatusSchema>;

export const MiniAppSchema = z.object({
  id: z.number(),
  name: z.string(),
  conversation_id: z.number(),
  developing: DevelopingSchema.nullable().optional(),
  installed_at: z.string().nullable().optional(),
  installation: InstallationSchema.nullable().optional(),
  history: z.array(InstallationSchema),
  status: MiniAppStatusSchema.nullable().optional(),
  type: z.string().nullable().optional(),
});
export type MiniApp = z.infer<typeof MiniAppSchema>;

export const ListMiniappResponseType = MiniAppSchema.extend({
  conversation: ConversationSchema,
});
export type ListMiniappResponseType = z.infer<typeof ListMiniappResponseType>;

export const SaveMiniAppRequestSchema = z.object({
  miniapp: MiniAppSchema,
});
export type SaveMiniAppRequest = z.infer<typeof SaveMiniAppRequestSchema>;

export const SaveMiniAppResponseSchema = z.object({
  success: z.boolean(),
});
export type SaveMiniAppResponse = z.infer<typeof SaveMiniAppResponseSchema>;

// sync user data handlers
export const SyncUserDataRequestSchema = z.object({
  startFrom: z.number().default(0),
});
export type SyncUserDataRequest = z.infer<typeof SyncUserDataRequestSchema>;

export const SyncUserDataResponseSchema = z.object({
  conversations: z.array(ConversationSchema),
  miniapps: z.array(ListMiniappResponseType),
});
export type SyncUserDataResponse = z.infer<typeof SyncUserDataResponseSchema>;

// Update MiniApp
export const UpdateMiniAppRequestSchema = z.object({
  id: z.number(),
  name: z.string().optional(),
  developing: DevelopingSchema.nullable().optional(),
  installation: InstallationSchema.nullable().optional(),
  status: MiniAppStatusSchema.nullable().optional(),
});
export type UpdateMiniAppRequest = z.infer<typeof UpdateMiniAppRequestSchema>;

export const UpdateMiniAppResponseSchema = z.object({
  success: z.boolean(),
});
export type UpdateMiniAppResponse = z.infer<typeof UpdateMiniAppResponseSchema>;
